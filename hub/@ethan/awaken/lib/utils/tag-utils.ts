export const Tags = {
  normalize: (raw: string) => raw.trim().toLowerCase().replace(/^#/, ""),
  display: (tag: string) => (tag.startsWith("#") ? tag : `#${tag}`),
  parse: (query: string) => {
    const tokens = query.split(/\s+/).filter(Boolean);
    return {
      tags: tokens
        .filter((t) => t.startsWith("#"))
        .map((t) => t.slice(1).toLowerCase()),
      terms: tokens
        .filter((t) => !t.startsWith("#"))
        .map((t) => t.toLowerCase()),
    } as const;
  },
} as const;

