import { TEXTUAL_MSG_TYPES } from "../lib/message-types.ts";
import { v4 as uuid } from "npm:uuid";
import { db } from "./db.ts";
import { Email<PERSON>obDAO, EmailJob, markJobCancelled } from "./organic-email-db.ts";
import { getProfileText, getCoachPrompt, decryptMessages } from "../action.ts";
import { sendSimpleEmail } from "./email-sender.ts";
import { saveMessage, prepMessageHistoryAction } from "../actions/db/conversation-actions.ts";
import { geminiRequest, claudeRequest, openRouterRequest } from "./helper.ts";
import { shouldSendOrganicEmail, EmailCheckContext } from "./should-send-email.ts";

// Type definitions
export type Stage = 1;
export type EmailType = "acknowledgement" | "quick_reflection" | "resource";

// Pure function to pick email type based on stage
export function pickType(stage: Stage): EmailType {
  const r = Math.random();
  // Stage 1 only: acknowledgement or resource
  return r < 0.6 ? "acknowledgement" : "resource";
}

// Calculate send time with timezone awareness
export async function scheduleAt(
  stage: Stage, 
  userId: string,
  now = new Date()
): Promise<Date> {
  // Get user timezone from database
  const user = await db
    .selectFrom("user")
    .select(["timezone"])
    .where("channelId", "=", userId)
    .executeTakeFirst();
    
  const timezone = user?.timezone || "America/New_York"; // fallback

  // Calculate base delay with randomization (in UTC)
  const minDelay = stage === 1 ? 6 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000;
  const maxDelay = stage === 1 ? 12 * 60 * 60 * 1000 : 48 * 60 * 60 * 1000;
  const randomDelay = minDelay + Math.random() * (maxDelay - minDelay);
  const targetTimeUtc = new Date(now.getTime() + randomDelay);

  // Utility: safely get timezone offset for a given instant
  const getTimeZoneOffsetMs = (tz: string, date: Date): number => {
    const locale = "en-US";
    const asTz = new Date(date.toLocaleString(locale, { timeZone: tz }));
    const asUtc = new Date(date.toLocaleString(locale, { timeZone: "UTC" }));
    return asTz.getTime() - asUtc.getTime();
  };

  // Utility: build a Date that represents a local wall-clock time in a timezone
  const makeDateInTimeZone = (
    tz: string,
    year: number,
    month: number, // 1-12
    day: number,
    hour: number,
    minute: number,
    second: number,
  ): Date => {
    // Start with the naive UTC millis for the local components
    const naiveUtcMs = Date.UTC(year, month - 1, day, hour, minute, second, 0);
    const probe = new Date(naiveUtcMs);
    // Get the timezone offset at that local moment
    const offsetMs = getTimeZoneOffsetMs(tz, probe);
    // Subtract offset to land on the correct UTC instant for that local time
    return new Date(naiveUtcMs - offsetMs);
  };

  // Read the target time as components in the user's timezone
  const dtf = new Intl.DateTimeFormat("en-US", {
    timeZone: timezone,
    hour12: false,
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  const parts = dtf.formatToParts(targetTimeUtc);
  const get = (type: string) => parts.find((p) => p.type === type)?.value ?? "0";
  let y = Number(get("year"));
  let m = Number(get("month"));
  let d = Number(get("day"));
  let h = Number(get("hour"));

  // Clamp to 6am-10pm window in user's timezone
  if (h < 6) {
    h = 6;
  } else if (h >= 22) {
    // Move to 6am next day (rollover handled by Date.UTC)
    // Construct a date at 06:00 of the next day via naive arithmetic
    const nextDayUtc = Date.UTC(y, m - 1, d + 1, 6, 0, 0, 0);
    const nextDayProbe = new Date(nextDayUtc);
    const offsetMs = getTimeZoneOffsetMs(timezone, nextDayProbe);
    const scheduledUtcFromNextDay = new Date(nextDayUtc - offsetMs);
    // Ensure we don't schedule earlier than the random target
    return new Date(Math.max(scheduledUtcFromNextDay.getTime(), targetTimeUtc.getTime()));
  }

  const scheduledUtc = makeDateInTimeZone(timezone, y, m, d, h, 0, 0);

  // Safety: never schedule earlier than the random-delay target
  return new Date(Math.max(scheduledUtc.getTime(), targetTimeUtc.getTime()));
}

// Handle user turn - schedule organic email
export async function handleUserTurn(
  userId: string,
  coachId: string,
  now = new Date()
) {
  try {
    console.log(`[ORGANIC_EMAIL] Starting handleUserTurn for user ${userId}, coach ${coachId}`);
    
    // Check if user has opted out of emails
    console.log(`[ORGANIC_EMAIL] Checking email preference for user ${userId}`);
    const user = await db
      .selectFrom("user")
      .select(["emailDaily"])
      .where("channelId", "=", userId)
      .executeTakeFirst();
    
    console.log(`[ORGANIC_EMAIL] User email preference:`, user?.emailDaily);
    
    if (user?.emailDaily === 0) {
      console.log(`[ORGANIC_EMAIL] User ${userId} has opted out of daily emails`);
      return;
    }
    
    console.log(`[ORGANIC_EMAIL] Canceling any pending emails for user ${userId}`);
    await EmailJobDAO.cancelPending(userId);
    
    const first: Stage = 1;
    const emailType = pickType(first);
    const sendAtTime = await scheduleAt(first, userId, now);
    
    console.log(`[ORGANIC_EMAIL] Scheduling stage ${first} email type "${emailType}" for ${sendAtTime}`);
    
    await EmailJobDAO.insert({
      id: uuid(),
      userId: userId,
      coachId: coachId,
      stage: first,
      type: emailType,
      sendAt: sendAtTime.toISOString(),
      createdAt: now.toISOString(),
    });
    
    console.log(`[ORGANIC_EMAIL] Successfully scheduled organic email for user ${userId}`);
  } catch (error) {
    console.error(`[ORGANIC_EMAIL] Error in handleUserTurn for user ${userId}:`, error);
    throw error; // Re-throw to be caught by caller
  }
}

// Build email content using LLM
async function buildEmail(job: EmailJob): Promise<{ subject: string; body: string }> {
  // Get user profile and recent messages
  const [profileData, messagesData] = await Promise.all([
    getProfileText(job.userId),
    decryptMessages(job.userId, true, "all", 25, [...TEXTUAL_MSG_TYPES])
  ]);
  
  const { profileText } = profileData;
  const { conversationText, timeElapsedNote } = prepMessageHistoryAction(messagesData);
  
  // Map email type to coach prompt key
  const promptKeyMap = {
    acknowledgement: "organicAcknowledgement",
    quick_reflection: "organicReflection", 
    resource: "organicResource"
  };
  
  // Get coach's prompt for this email type
  let systemPrompt: string | undefined;
  try {
    const coachPromptResponse = await getCoachPrompt(
      job.coachId, 
      promptKeyMap[job.type]
    );
    systemPrompt = coachPromptResponse.SystemPrompt;
  } catch (error) {
    console.log(`[ORGANIC_EMAIL] No prompt configured for ${promptKeyMap[job.type]}, using fallback`);
    // systemPrompt will remain undefined and we'll use the fallback
  }
  
  // Get coach metadata to determine which model to use (direct DB query for cron context)
  const coach = await db
    .selectFrom("coaches")
    .selectAll()
    .where("name", "=", job.coachId)
    .executeTakeFirst();
  
  // Extract model configuration
  let modelProvider = "google"; // Default to gemini for emails
  let modelName = "gemini-2.5-pro"; // Default model
  let determinedModelType: "claude" | "gemini" | "openrouter" = "gemini";
  
  if (coach?.metadata) {
    const metadata = typeof coach.metadata === 'string' 
      ? JSON.parse(coach.metadata) 
      : coach.metadata;
    
    // Check for messageModel configuration (preferred for messages)
    if (metadata.messageModel?.model) {
      const msgConfig = metadata.messageModel;
      modelProvider = (msgConfig.provider || "google").toLowerCase();
      modelName = msgConfig.model;
      
      // Determine model type
      if (modelProvider === "google") {
        determinedModelType = "gemini";
      } else if (modelProvider === "openrouter") {
        determinedModelType = "openrouter";
      } else if (modelProvider === "anthropic") {
        determinedModelType = "claude";
      }
    }
    // Fallback to legacy model config
    else if (metadata.model && Array.isArray(metadata.model) && metadata.model.length > 0) {
      const modelConfig = metadata.model[0];
      modelProvider = modelConfig.provider?.toLowerCase() || modelProvider;
      modelName = modelConfig.model || modelName;
      
      if (modelProvider === "google") {
        determinedModelType = "gemini";
      } else if (modelProvider === "openrouter") {
        determinedModelType = "openrouter";
      } else if (modelProvider === "anthropic") {
        determinedModelType = "claude";
      }
    }
  }
  
  console.log(`[ORGANIC_EMAIL] Using model: ${modelName} (${determinedModelType}) from provider: ${modelProvider}`);
  
  // Prepare prompt
  const userPrompt = `
    <CLIENT_PROFILE>${profileText}</CLIENT_PROFILE>
    <MESSAGE_HISTORY>${conversationText}</MESSAGE_HISTORY>
    <TIME_ELAPSED>${timeElapsedNote}</TIME_ELAPSED>
  `;
  
  // Fallback prompt if none configured
  const fallbackPrompt = `You are ${job.coachId}, a transformational AI coach. Generate a brief, warm email following up on our recent conversation. 

Format your response with XML tags:
<SUBJECT>Your email subject line here</SUBJECT>
<BODY>
Your email body content here
</BODY>`;
  const finalSystemPrompt = systemPrompt || fallbackPrompt;
  
  let emailContent: string;
  
  // Handle different model types
  if (determinedModelType === "claude") {
    // Claude format - claudeRequest expects (systemPrompt, messageArray)
    const messageArray = [{
      role: "user",
      content: userPrompt
    }];
    
    // claudeRequest returns a string directly, not a Response object
    emailContent = await claudeRequest(finalSystemPrompt, messageArray);
    
  } else if (determinedModelType === "openrouter") {
    // OpenRouter format - call with correct signature
    emailContent = await openRouterRequest(modelName, finalSystemPrompt, userPrompt);
    
  } else {
    // Gemini format (default)
    const geminiPayload = {
      system_instruction: {
        parts: { text: finalSystemPrompt }
      },
      contents: [{
        role: "user",
        parts: [{ text: userPrompt }]
      }],
      generationConfig: {
        temperature: 1.0
      },
      safetySettings: [
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" }
      ]
    };
    
    const geminiResponse = await geminiRequest([geminiPayload], modelName);
    const responseData = await geminiResponse.json();
    
    // Handle string "true"/"false" values from Gemini
    if (responseData.success !== "true" || !responseData.text1) {
      console.error("[ORGANIC_EMAIL] Gemini response error:", responseData);
      throw new Error(`Failed to generate email content: ${responseData.error || 'No text1 in response'}`);
    }
    
    emailContent = responseData.text1;
  }
  
  // Extract subject and body from XML tags (like in generateDailyAwakenings)
  const subjectMatch = emailContent.match(/<SUBJECT>([\s\S]*?)<\/SUBJECT>/);
  const bodyMatch = emailContent.match(/<BODY>([\s\S]*?)<\/BODY>/);
  
  let subject = "Following up on our conversation"; // Default fallback
  if (subjectMatch && subjectMatch[1]) {
    subject = subjectMatch[1].trim();
  }
  
  let body = emailContent; // Fallback to full content if no tags found
  if (bodyMatch && bodyMatch[1]) {
    body = bodyMatch[1].trim();
  }
  
  return { subject, body };
}

// Process organic emails - main cron function
export async function processOrganicEmails() {
  const now = new Date();
  const jobs = await db
    .selectFrom("emailJob")
    .selectAll()
    .where("sendAt", "<=", now.toISOString())
    .where("sentAt", "is", null)
    .where("cancelled", "=", 0)
    .limit(500)
    .execute();

  for (const job of jobs) {
    try {
      // Get user info for email
      const user = await db
        .selectFrom("user")
        .select(["email", "firstName"])
        .where("channelId", "=", job.userId)
        .executeTakeFirst();
        
      if (!user?.email) {
        console.log(`[ORGANIC_EMAIL] No email found for user ${job.userId}`);
        continue;
      }
      
      // Check if email is relevant before building
      console.log(`[ORGANIC_EMAIL] Starting relevance check for job ${job.id}, user ${job.userId}`);
      const profileData = await getProfileText(job.userId);
      const messages = await decryptMessages(
        job.userId,
        true,
        "all",
        25,
        [...TEXTUAL_MSG_TYPES],
      );
      console.log(`[ORGANIC_EMAIL] Retrieved ${messages.length} messages for relevance check`);
      
      const recentMsgsStr = messages
        .map((m: any) => m.Content)
        .join("\n")
        .slice(0, 20000);
      
      const gateCtx: EmailCheckContext = {
        userId: String(job.userId),
        profile: profileData?.profileText ?? null,
        recentMessages: recentMsgsStr,
      };
      
      console.log(`[ORGANIC_EMAIL] Calling shouldSendOrganicEmail gate`);
      if (!(await shouldSendOrganicEmail(gateCtx))) {
        await markJobCancelled(job.id!);
        console.log(`[EMAIL-GATE] Job ${job.id} cancelled (UNNEEDED) - email will not be sent`);
        continue;
      }
      console.log(`[ORGANIC_EMAIL] Email gate passed - proceeding to build email`);
      
      // Build email content
      const { subject, body } = await buildEmail(job);
      
      // Get coach name for email sender
      const coach = await db
        .selectFrom("coaches")
        .select(["name"])
        .where("name", "=", job.coachId)
        .executeTakeFirst();
      
      // Save to conversation table
      await saveMessage(
        job.userId,
        "assistant",
        body,
        new Date().toISOString(),
        "Default",
        "proactive_message", // new message type
        job.coachId
      );
      
      // Send email as simple, friend-like format
      const coachName = coach?.name || job.coachId;
      await sendSimpleEmail(user.email, subject, body, coachName);
      
      // Mark job as sent
      await db
        .updateTable("emailJob")
        .set({ sentAt: now.toISOString() })
        .where("id", "=", job.id)
        .execute();

      // Stage 2+ disabled for now: do not schedule follow-ups
    } catch (error) {
      console.error(`[ORGANIC_EMAIL] Error processing job ${job.id}:`, error);
      // Natural retry on next cron run since sent_at remains null
    }
  }
}