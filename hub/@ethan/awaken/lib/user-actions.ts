"use server";
export type ActionType = "message" | "call";

export interface ActionDetails {
    name: string;
    description: string;
    costPerUnit: number;
    // cost per unit in essence
}

export const ACTIONS: Record<ActionType, ActionDetails> = {
    message: {
        name: "Message",
        description: "Send a message",
        costPerUnit: 0.33,
    },
    call: {
        name: "Call",
        description: "Call assistant",
        costPerUnit: 1,
    },
};

export const getActionCost = (action: ActionType, units: number) => {
    return ACTIONS[action].costPerUnit * units;
};
