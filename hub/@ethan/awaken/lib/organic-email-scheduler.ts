import { v4 as uuid } from "npm:uuid";
import { db } from "./db.ts";
import { EmailJobDAO } from "./organic-email-db.ts";

// Type definitions
export type Stage = 1 | 2;
export type EmailType = "acknowledgement" | "quick_reflection" | "resource";

// Helper function for random number in range
function rand(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

// Pure function to pick email type based on stage
export function pickType(stage: Stage): EmailType {
  const r = Math.random();
  if (stage === 1) {
    // Stage 1: only acknowledgement or resource (no quick_reflection)
    return r < 0.6 ? "acknowledgement" : "resource";
  }
  // Stage 2: prefer quick_reflection, then resource, rare acknowledgement
  return r < 0.6 ? "quick_reflection" : r < 0.9 ? "resource" : "acknowledgement";
}

// Calculate send time with timezone awareness
export async function scheduleAt(
  stage: Stage, 
  userId: string,
  now = new Date()
): Promise<Date> {
  // Get user timezone from database
  const user = await db
    .selectFrom("user")
    .select(["timezone"])
    .where("channelId", "=", userId)
    .executeTakeFirst();
    
  const timezone = user?.timezone || "America/New_York"; // fallback
  
  // Calculate base delay with randomization (in UTC)
  const minDelay = stage === 1 ? 6 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000;
  const maxDelay = stage === 1 ? 12 * 60 * 60 * 1000 : 48 * 60 * 60 * 1000;
  const randomDelay = minDelay + Math.random() * (maxDelay - minDelay);
  const targetTimeUtc = new Date(now.getTime() + randomDelay);

  // Utility: safely get timezone offset for a given instant
  const getTimeZoneOffsetMs = (tz: string, date: Date): number => {
    const locale = "en-US";
    const asTz = new Date(date.toLocaleString(locale, { timeZone: tz }));
    const asUtc = new Date(date.toLocaleString(locale, { timeZone: "UTC" }));
    return asTz.getTime() - asUtc.getTime();
  };

  // Utility: build a Date that represents a local wall-clock time in a timezone
  const makeDateInTimeZone = (
    tz: string,
    year: number,
    month: number, // 1-12
    day: number,
    hour: number,
    minute: number,
    second: number,
  ): Date => {
    const naiveUtcMs = Date.UTC(year, month - 1, day, hour, minute, second, 0);
    const probe = new Date(naiveUtcMs);
    const offsetMs = getTimeZoneOffsetMs(tz, probe);
    return new Date(naiveUtcMs - offsetMs);
  };

  // Read the target time as components in the user's timezone
  const dtf = new Intl.DateTimeFormat("en-US", {
    timeZone: timezone,
    hour12: false,
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  const parts = dtf.formatToParts(targetTimeUtc);
  const get = (type: string) => parts.find((p) => p.type === type)?.value ?? "0";
  let y = Number(get("year"));
  let m = Number(get("month"));
  let d = Number(get("day"));
  let h = Number(get("hour"));

  // Clamp to 6am-10pm window in user's timezone
  if (h < 6) {
    h = 6;
  } else if (h >= 22) {
    const nextDayUtc = Date.UTC(y, m - 1, d + 1, 6, 0, 0, 0);
    const nextDayProbe = new Date(nextDayUtc);
    const offsetMs = getTimeZoneOffsetMs(timezone, nextDayProbe);
    const scheduledUtcFromNextDay = new Date(nextDayUtc - offsetMs);
    return new Date(Math.max(scheduledUtcFromNextDay.getTime(), targetTimeUtc.getTime()));
  }

  const scheduledUtc = makeDateInTimeZone(timezone, y, m, d, h, 0, 0);
  return new Date(Math.max(scheduledUtc.getTime(), targetTimeUtc.getTime()));
}

// Handle user turn - schedule organic email
export async function handleUserTurn(
  userId: string,
  coachId: string,
  now = new Date()
) {
  try {
    console.log(`[ORGANIC_EMAIL] Starting handleUserTurn for user ${userId}, coach ${coachId}`);
    
    // Check if user has opted out of emails
    console.log(`[ORGANIC_EMAIL] Checking email preference for user ${userId}`);
    const user = await db
      .selectFrom("user")
      .select(["emailDaily"])
      .where("channelId", "=", userId)
      .executeTakeFirst();
    
    console.log(`[ORGANIC_EMAIL] User email preference:`, user?.emailDaily);
    
    if (user?.emailDaily === 0) {
      console.log(`[ORGANIC_EMAIL] User ${userId} has opted out of daily emails`);
      return;
    }
    
    console.log(`[ORGANIC_EMAIL] Canceling any pending emails for user ${userId}`);
    await EmailJobDAO.cancelPending(userId);
    
    const first: Stage = 1;
    const emailType = pickType(first);
    const sendAtTime = await scheduleAt(first, userId, now);
    
    console.log(`[ORGANIC_EMAIL] Scheduling stage ${first} email type "${emailType}" for ${sendAtTime}`);
    
    await EmailJobDAO.insert({
      id: uuid(),
      userId: userId,
      coachId: coachId,
      stage: first,
      type: emailType,
      sendAt: sendAtTime.toISOString(),
      createdAt: now.toISOString(),
    });
    
    console.log(`[ORGANIC_EMAIL] Successfully scheduled organic email for user ${userId}`);
  } catch (error) {
    console.error(`[ORGANIC_EMAIL] Error in handleUserTurn for user ${userId}:`, error);
    throw error;
  }
}