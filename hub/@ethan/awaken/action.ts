"use server";

import { MESSAGE_TYPES, UI_DEFAULT_TYPES, TEXTUAL_MSG_TYPES } from "./lib/message-types.ts";
import { createClient } from "./db/client.ts";
import Reframe from "@";
import CryptoJS from "npm:crypto-js";
import stripe from "./lib/stripe-lib.ts";
import { sql } from "npm:kysely";
import { soulListenerSystemPrompt } from "./lib/server-constants.ts";
import { db, getTotalEssenceBalance, getUserData, initiateUserProfile } from "./lib/db.ts";
import { 
  metaPrompt1, 
  metaPrompt2, 
  initPrompt, 
  updateProfilePrompt, 
  updateInsightPrompt 
} from "./lib/server-constants.ts";
import { PLANS } from "./lib/plans.ts";
import { PlanType } from "./lib/plans.ts";
import {
  getAllMessagesRaw,
  getFirstMessage as getFirstMessageFromDb,
  saveMessage,
  getMessages,
  getMessagesSince,
  uploadAudio
} from "./actions/db/conversation-actions.ts";
import { 
  getLatestNotice as getLatestNoticeFromDb, 
  getAllNotices as getAllNoticesFromDb, 
  createNotice as createNoticeInDb, 
  updateLastSeenNotice as updateLastSeenNoticeInDb 
} from "./lib/notice-db.ts";
import { getActionCost } from "./lib/user-actions.ts";
import { createCheckoutSession } from "./actions/stripe-actions.ts";
import { getUserInfo } from "./lib/get-user-info.ts";
import { verifyUserPermission } from "./lib/auth-helper.ts";
import { claudeRequest, geminiRequest, openRouterRequest, claudeRequestRaw } from "./lib/helper.ts";
import { shouldSendAwakeningOnSchedule } from "./lib/awakening-helper.ts";
import { getCoachAction } from "./actions/db/coach-actions.ts";

// Add feature flag to easily toggle meta-feedback functionality on/off
const METAFEEDBACK_ENABLED = true;

export const getProfileText = async (channelId: string): Promise<{ profileText: string; }> => {
  try {
    const profileEntry = await db
      .selectFrom('profile_text')
      .select(['content'])
      .where('channelId', '=', channelId)
      .executeTakeFirst();

    if (!profileEntry || profileEntry.content === null) {
      throw new Error('No profile found');
    }

    // console.log("[NEW PROFILE TEXT]", decryptProfile(profileEntry.content));
    return {
      profileText: decryptProfile(profileEntry.content)
    };
  } catch (error) {
    console.error(`[getProfileText] Error fetching profile for channelId ${channelId}:`, error);
    // Re-throw the error or handle it as appropriate for your application
    if (error instanceof Error && error.message === 'No profile found') {
        // Specifically re-throw 'No profile found' if you want to handle it distinctly upstream
        throw error;
    }
    throw new Error(`Failed to get profile text for channelId ${channelId}`);
  }
};

export const setProfileText = async (channelId: string, profileText: string): Promise<void> => {
  const encryptedProfileText = encryptProfile(profileText);
  const newCreatedAt = new Date().toISOString();

  try {
    await db.transaction().execute(async (trx) => {
      // 1. Fetch the current profile text for the channel, if it exists.
      const currentProfile = await trx
        .selectFrom('profileText')
        .selectAll()
        .where('channelId', '=', channelId)
        .executeTakeFirst();

      // 2. If it exists, dump it to the profile_text_dump table.
      if (currentProfile && currentProfile.content) {
        await trx
          .insertInto('profile_text_dump')
          .values({
            channel_id: currentProfile.channelId,
            content: currentProfile.content, // This is already encrypted
            created_at: currentProfile.createdAt,
          })
          .execute();
      }

      // 3. Upsert the new profile text into the profile_text table.
      // SQLite dialect for Kysely uses onConflict for upserts.
      await trx
        .insertInto('profile_text')
        .values({
          channel_id: channelId,
          content: encryptedProfileText,
          created_at: newCreatedAt, // Set new creation/update time
        })
        .onConflict((oc) => oc
          .column('channelId')
          .doUpdateSet({
            content: encryptedProfileText,
            created_at: newCreatedAt, // Update timestamp on conflict as well
          })
        )
        .execute();
    });
  } catch (error) {
    console.error(`[setProfileText] Error setting profile for channelId ${channelId}:`, error);
    throw new Error(`Failed to set profile text for channelId ${channelId}`);
  }
};

// Define the response structure for the new function
interface CoachPromptData {
  SystemPrompt: string;
}

interface CoachPromptAndToolsResponse {
  promptIdentifier: string | null;
  promptData: CoachPromptData | null;
  toolsConfig: any[]; // Define a more specific type later if needed
}

/**
 * Fetches the prompt identifier, prompt content, and tool configurations for a specific coach and prompt type.
 * Handles backwards compatibility with the old string-based prompt format.
 */
export const getCoachPromptAndTools = async (coachId: string, promptType: string): Promise<CoachPromptAndToolsResponse> => {
  console.log("[PROMPT_TOOLS] Fetching prompt and tools:", { coachId, promptType });

  let promptIdentifier: string | null = null;
  let promptData: CoachPromptData | null = null;
  let toolsConfig: any[] = [];

  try {
    // 1. Fetch the coach's prompts configuration
    const coach = await db
      .selectFrom("coaches")
      .select(["prompts"])
      .where("name", "=", coachId)
      .executeTakeFirst();

    if (!coach) {
      console.warn("[PROMPT_TOOLS] Coach not found:", coachId);
      return { promptIdentifier, promptData, toolsConfig }; // Return default empty state
    }

    // 2. Parse the prompts JSON configuration
    let promptsMapping;
    try {
      promptsMapping = typeof coach.prompts === 'string'
        ? JSON.parse(coach.prompts)
        : coach.prompts; // Assume it's already an object if not a string
      if (promptsMapping === null || typeof promptsMapping !== 'object') {
        throw new Error("Parsed prompts mapping is not an object.");
      }
    } catch (error) {
      console.error("[PROMPT_TOOLS] Error parsing prompts mapping:", error);
      promptsMapping = {}; // Default to empty object on error
    }

    // 3. Determine prompt identifier and tools based on format
    const promptConfig = promptsMapping?.[promptType];

    if (typeof promptConfig === 'string') {
      // Format A (Old/No Tools)
      promptIdentifier = promptConfig;
      toolsConfig = [];
      console.log("[PROMPT_TOOLS] Using old format (string):", promptIdentifier);
    } else if (typeof promptConfig === 'object' && promptConfig !== null && promptConfig.promptId) {
      // Format B (New/With Tools)
      promptIdentifier = promptConfig.promptId;
      toolsConfig = promptConfig.tools || [];
      console.log("[PROMPT_TOOLS] Using new format (object):", { promptIdentifier, toolsConfig });
    } else {
      console.warn("[PROMPT_TOOLS] No valid config found for prompt type:", promptType, "Config:", promptConfig);
      // Keep defaults: promptIdentifier = null, toolsConfig = []
    }

    // 4. Fetch the actual prompt content if an identifier was found
    if (promptIdentifier) {
      console.log("[PROMPT_TOOLS] Querying prompts table for:", promptIdentifier);
      const promptRecord = await db
        .selectFrom("prompts")
        .selectAll() // Select all to handle potential column name variations
        .where("name", "=", promptIdentifier)
        .executeTakeFirst();

      if (promptRecord) {
        // Reuse column detection logic from original getCoachPrompt
        const textColumn = Object.keys(promptRecord).find(key => key.toLowerCase() === 'prompttext') || 'promptText';

        let systemPrompt = (promptRecord[textColumn] as string | null) || '';

        // Log before replacement
        console.log("[PROMPT_TOOLS] Before COACH_NAME replacement:", {
          systemPrompt: systemPrompt.substring(0, 100) + "..."
        });

        // Replace {{COACH_NAME}}
        systemPrompt = systemPrompt.replace(/{{COACH_NAME}}/g, coachId);

        // Log after replacement
        console.log("[PROMPT_TOOLS] After COACH_NAME replacement:", {
          systemPrompt: systemPrompt.substring(0, 100) + "..."
        });

        promptData = {
          SystemPrompt: systemPrompt,
        };
        console.log("[PROMPT_TOOLS] Successfully retrieved prompt content for:", promptIdentifier);
      } else {
        console.warn("[PROMPT_TOOLS] Prompt not found in prompts table:", promptIdentifier);
        // promptData remains null
      }
    }

    return { promptIdentifier, promptData, toolsConfig };

  } catch (error) {
    console.error("[PROMPT_TOOLS] Error fetching prompt and tools:", error);
    // Return default empty state on error
    return { promptIdentifier: null, promptData: null, toolsConfig: [] };
  }
};

/**
 * Fetches the prompt content (SystemPrompt) for a given coach and prompt type.
 * Maintains compatibility with callers that don't need tool info.
 * This function now calls getCoachPromptAndTools internally.
 */
export const getCoachPrompt = async (coachId: string, promptType: string): Promise<CoachPromptData> => {
  try {
    const { promptData } = await getCoachPromptAndTools(coachId, promptType);

    // Return the prompt data or default empty strings if not found
    return promptData || { SystemPrompt: '' };

  } catch (error) {
    // Log the error originating from getCoachPromptAndTools if necessary
    // The error is already logged inside getCoachPromptAndTools, so maybe just re-throw or return default
    console.error("[PROMPT - Deprecated Wrapper] Error occurred:", error);
    // Depending on desired behavior, either throw the error or return default
    // throw error;
     return { SystemPrompt: '' }; // Return default empty strings on error
  }
};

export const decryptProfile = (profileText: string) => {
  const secretKey = Reframe.env.SECRET_KEY;

  const decryptedProfileText = profileText
    ? CryptoJS.AES.decrypt(profileText, secretKey).toString(
        CryptoJS.enc.Utf8
      )
    : null;
  return decryptedProfileText;
}

// export const signUp = async (email: string, password: string) => {
//   console.log("sign up");
//   const created_at = new Date().toISOString();
//   // Generate 8 digit random number
//   const channelId = Math.floor(10000000 + Math.random() * 90000000);
//   const users = await db
//     .insertInto("user")
//     .values({ 
//       email,
//       password,
//       createdAt: created_at,
//       channelId,
//       monthlyEssenceBalance: PLANS.free.essencePerMonth
//     })
//     .execute();

//   return { users };
// }

export const dummy = async () => {
  console.log("dummy");
  return { dummy: "dummy" };
}

export const getAllMessages = async (channelId: string | number, limit?: number, before?: string, coachName: string = "all", messageTypes: string[] = [...UI_DEFAULT_TYPES]) => {
  if(!(await verifyUserPermission(channelId.toString()))) return null;
  console.log("get all messages");
  console.log(`Params: channelId=${channelId}, limit=${limit || 10}, before=${before || 'none'}, coachName=${coachName}`);
  
  if (coachName === "all") {
    console.log("[DB] 📊 Will show messages from ALL coaches (no filtering)");
  } else {
    console.log(`[DB] 🔍 Will filter messages for coach: "${coachName}"`);
  }

  try {
    // First try to get messages from SQL database
    console.log("[DB] Fetching messages from SQL database");
    // Pass the before parameter directly - the function should handle undefined internally
    const messages = await getMessages(channelId, limit || 25, before as string | undefined, true, coachName, messageTypes);
    console.log(`[DB] Retrieved ${messages.length} messages from database`);
    
    // Log a sample of coaches to verify
    if (messages.length > 0) {
      const coaches = [...new Set(messages.map(m => m.CoachName))];
      console.log(`[DB] Coaches found in result: ${coaches.join(', ')}`);
    }
    
    if (messages.length > 0) {
      // CRITICAL: Do NOT modify the date format here - keep the ISO format
      // This prevents any locale-specific formatting which causes inconsistency
      console.log("[DB] Returning messages from SQL database with ISO dates preserved");
      return messages;
    }
    return [];
    
    // If no messages in SQL database, try the Make integration for backward compatibility
    console.log("[LEGACY] No messages in SQL database, trying Make integration");
    const makeUrl = `https://hook.eu1.make.com/o28dk26eszlh868sab5ip51fy9f4ftgc?userId=${channelId}&limit=${limit || 25}&encrypted=false${before ? `&before=${encodeURIComponent(before)}` : ''}`;
    
    const response = await fetch(makeUrl);
    if (!response.ok) {
      throw new Error(`Make API error: ${response.status}`);
    }
    
    const allMessages = await response.json();
    console.log(`[LEGACY] Retrieved ${allMessages.length} messages from Make integration`);

    // CRITICAL: For legacy data, DON'T format dates to locale string
    // Let client-side handle any formatting needed for display
    return allMessages.reverse();
  } catch (error) {
    console.error("Error fetching messages:", error);
    throw error;
  }
}

/**
 * Get messages since a specific timestamp for incremental sync
 */
export const getMessagesSinceTimestamp = async (
  channelId: string | number,
  since: string,
  limit?: number,
  coachName: string = "all",
  messageTypes: string[] = [...UI_DEFAULT_TYPES]
) => {
  if(!(await verifyUserPermission(channelId.toString()))) return null;
  console.log(`[INCREMENTAL] Getting messages since: ${since}`);
  console.log(`Params: channelId=${channelId}, limit=${limit || 20}, since=${since}, coachName=${coachName}`);

  try {
    // Use the new getMessagesSince function from conversation-actions
    const messages = await getMessagesSince(
      channelId,
      since,
      limit || 20,
      true, // decrypt content
      coachName,
      messageTypes
    );

    console.log(`[INCREMENTAL] Retrieved ${messages.length} messages since ${since}`);
    return messages;

  } catch (error) {
    console.error("[INCREMENTAL] Error fetching messages since timestamp:", error);
    throw error;
  }
}

const generateFirstMessage = async (channelId: string) => {
  // Fetch user info for personalization
  const userInfo = await getUserInfo({ channelId });
  const firstName = userInfo?.name?.split(" ")[0];
  if (!firstName) throw new Error("User not found");

  // Look-up which coach the user has selected (defaults to Kokoro)
  const userRecord = await db
    .selectFrom("user")
    .select(["selectedCoach"])
    .where("channelId", "=", Number(channelId))
    .executeTakeFirst();
  const coachName = userRecord?.selectedCoach || "Kokoro";

  // ----- 1.  Hard-coded welcome text per coach -----
  let firstMessage: string;
  if (coachName === "JP AI") {
    firstMessage = `Hi ${firstName}, it's awesome to be with you here in Awaken.
    
I am here to champion you, to see you fully and to help you create who and what you are, so that you can experience a life of greater freedom, love and power.

What would you love to create today?`;
  } else if (coachName === "Fred AI") {
    firstMessage = `Hi ${firstName}, welcome!

I'm here to awaken you to what's true about you. Not a better story, but the end of the one that's been running you.

When you see through the misunderstanding of who you are, there's a freedom and peace that was here all along.

Are you ready to begin?`;
  } else {
    firstMessage = `Hi ${firstName}, welcome! Do you remember a moment when something changed how you see yourself or the world?
  
Here, you can awaken when it matters most – in your daily life.

Awaken is a private, sacred space just for you.

What brings you here today?`;
  }

  // ----- 2.  Pick coach voice if available (otherwise generateAudio uses its own default) -----
  let selectedVoiceId: string | undefined;
  try {
    const coach = await getCoachAction(coachName, channelId);
    selectedVoiceId = coach?.selectedVoice?.voice_id;
  } catch (err) {
    console.warn("[FIRST_MESSAGE] Could not fetch coach voice; using default", err);
  }

  const audioBuffer = await generateAudio(firstMessage, selectedVoiceId ? { id: selectedVoiceId } : undefined);
  const audioBlob = new Blob([audioBuffer], { type: "audio/mpeg" });
  const audioUrl = await uploadAudio(audioBlob, channelId);

  const encryptUserData = await saveMessage(
    channelId,
    "assistant",
    firstMessage,
    new Date().toISOString(),
    "Default", // status (default "Default")
    "message",
    coachName,
    audioUrl
  );

  return {
    Id: encryptUserData?.id,
    ...encryptUserData?.fields,
    Content: firstMessage,
  };
};

export const getFirstMessage = async (channelId: string) => {
  if(!(await verifyUserPermission(channelId))) return null;
  
  try {
    // First try to get first message from SQL database
    console.log("[DB] Fetching first message from SQL database");
    const firstMessage = await getFirstMessageFromDb(channelId);
    
    if (firstMessage) {
      console.log("[DB] Found first message in SQL database");
      return firstMessage;
    }
    
    const firstMessageResponse = await generateFirstMessage(channelId);
    console.log("[ACTION] Generated first message", firstMessageResponse);
    
    return firstMessageResponse;
  } catch (error) {
    console.error("Error fetching first message:", error);
    throw error;
  }
}

export const initiateSubscription = async (request: Request) => {
  console.log('[SERVER] Subscription request received');
  
  try {
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('application/json')) {
      throw new Error('Invalid content type');
    }

    const { email, channelId, plan, isYearly } = await request.json();

    if (!email || !channelId || !plan) {
      throw new Error('Missing required fields');
    }

    // Don't append _monthly/_yearly here since plan already includes it
    const session = await createCheckoutSession(channelId, plan, email);

    return Response.json({ url: session.url });

  } catch (error) {
    console.error('[SERVER] Error:', error);
    return Response.json(
      { error: error.message || 'Internal server error' },
      { status: error.status || 500 }
    );
  }
};

// Update subscription usage
export const updateSubscriptionUsage = async (userId: string, messageCount: number = 0, callMinutes: number = 0) => {
  try {
    await db.transaction().execute(async (trx) => {
      await trx
        .updateTable("user")
        .set((eb) => ({
          messagesThisPeriod: eb("messagesThisPeriod", "+", messageCount),
          callMinsThisPeriod: eb("callMinsThisPeriod", "+", callMinutes),
          totalMessages: eb("totalMessages", "+", messageCount),
          totalCallMins: eb("totalCallMins", "+", callMinutes)
        }))
        .where("channelId", "=", Number(userId))
        .execute();
    });
  } catch (error) {
    console.error("[USAGE] Failed to update usage:", error);
    throw error;
  }
};

export async function generateAudio(
  text: string,
  voice: { id: string; model?: string } = { id: "OPp59xqerF9pc477FJM8" }
) {
  const { id: voiceId, model = "eleven_flash_v2" } = voice;

  const response = await fetch(
    `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "xi-api-key": Reframe.env.ELEVENLABS_API_KEY,
      },
      body: JSON.stringify({
        text,
        model_id: model,
        voice_settings: {
          stability: 0.35,
          similarity_boost: 0.5,
        },
      }),
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    console.error("ElevenLabs API error:", errorText);
    throw new Error(`TTS failed: ${response.status} ${errorText}`);
  }
  return response.arrayBuffer();
}

export const getAppVersion = async () => {
  const version = '2.6.6';
  const timestamp = Date.now();
  
  console.log('[SERVER] Version check requested:', {
    version,
    timestamp: new Date(timestamp).toLocaleString()
  });
  
  return { version, timestamp };
};

export const getLatestNotice = async (channelId: string) => {
  console.log('[SERVER] Fetching latest notice for channel:', channelId);
  
  try {
    return await getLatestNoticeFromDb(channelId);
  } catch (error) {
    console.error('[SERVER] Error fetching notice:', error);
    throw error;
  }
};

/**
 * Send a daily awakening email using Postmark
 * 
 * @param email - Recipient email address
 * @param message - Daily awakening message content
 * @param userName - Optional user name for personalization
 * @returns Response from Postmark API
 */
export const sendDailyAwakeningEmail = async (email: string, subject: string, message: string, userName?: string) => {
  try {
    console.log("[EMAIL] Sending daily awakening email to:", email);
    console.log("[EMAIL] Subject received:", subject);
    
    // Get current date for email
    const today = new Date();
    const dateString = today.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    
    // Import the email template generator
    const { generateDailyAwakeningEmailHtml } = await import("./lib/email-templates.ts");
    
    // Generate HTML email content using the template
    const emailHtml = generateDailyAwakeningEmailHtml(message, userName, dateString, subject);

    const postmarkBody = {
      From: '<EMAIL>',
      To: email,
      Subject: `${subject}`,
      HtmlBody: emailHtml,
      TextBody: message,
      MessageStream: 'outbound'
    };

    console.log("[EMAIL] Sending with subject:", postmarkBody.Subject);

    // Send email using Postmark API
    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': Reframe.env.POSTMARK_API_TOKEN
      },
      body: JSON.stringify(postmarkBody)
    });

    const result = await response.json();
    console.log("[EMAIL] Email sent successfully:", result);
    console.log("[EMAIL] Final subject sent:", postmarkBody.Subject);
    return result;
  } catch (error) {
    console.error("[EMAIL] Error sending email:", error);
    throw error;
  }
};

export const getAllNotices = async () => {
  console.log('[SERVER] Fetching all notices');
  
  try {
    return await getAllNoticesFromDb();
  } catch (error) {
    console.error('[SERVER] Error fetching notices:', error);
    return [];
  }
};

export const createNotice = async (message: string, userEmail: string) => {
  console.log('[SERVER] Creating notice:', { message, userEmail });
  
  try {
    return await createNoticeInDb(message, userEmail);
  } catch (error) {
    console.error('[SERVER] Error creating notice:', error);
    throw error;
  }
};

export const updateLastSeenNotice = async (channelId: string) => {
  console.log('[SERVER] Updating last seen notice for channel:', channelId);
  
  try {
    return await updateLastSeenNoticeInDb(channelId);
  } catch (error) {
    console.error('[SERVER] Error updating last seen notice:', error);
    throw error;
  }
};

export const updateTimeZone = async (channelId: string, timeZone: string) => {
  if(!(await verifyUserPermission(channelId))) return null;
  console.log("[TIMEZONE] Updating user timezone:", { channelId, timeZone });

  try {
    await db
      .updateTable("user")
      .set({ timezone: timeZone })
      .where("channel_id", "=", Number(channelId))
      .execute();

    return { success: true };
  } catch (error) {
    console.error("[TIMEZONE] Error updating timezone:", error);
    throw error;
  }
};

export const testDailyDigestAction = async () => {
  console.log("[ACTION] Testing daily digest functionality...");

  try {
    const { testDailyDigest, testMockEmail } = await import("./lib/test-daily-digest.ts");

    // Run the mock email test first (doesn't require database)
    testMockEmail();

    // Then run the full test with real data
    await testDailyDigest();

    return { success: true, message: "Daily digest test completed successfully" };
  } catch (error) {
    console.error("[ACTION] Error testing daily digest:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Decrypt messages for a channel
 * 
 * @param channelId - The user's channel ID
 * @param parse - Whether to return as objects (true) or JSON string (false)
 * @param coachName - Optional coach name to filter messages (defaults to default coach)
 *                    Use "all" to show messages from all coaches (typically for UI display)
 *                    Otherwise, use a specific coach name (or undefined for default coach) for AI context
 * @param messageCount - Optional number of messages to return (defaults to 25)
 * @param messageType - Optional message type to filter by (e.g., "message", "daily_awakening")
 * @returns Decrypted messages as objects or JSON string
 */
export const decryptMessages = async (channelId: string, parse: boolean = false, coachName?: string, messageCount: number = 25, messageTypes?: string[]) => {
  console.log("[DECRYPT] Starting decryption process");
  console.log("[DECRYPT] Request params:", { channelId, parse, coachName, messageCount, messageTypes });
  
  if (coachName === "all") {
    console.log("[DECRYPT] 📂 Fetching messages from ALL coaches");
  } else if (coachName) {
    console.log("[DECRYPT] 🔍 Explicitly using coach:", coachName);
  } else {
    console.log("[DECRYPT] ⚠️ No coach specified, will use default coach");
  }
  
  try {
    // Get messages from SQL database
    console.log("[DECRYPT] Fetching messages from SQL database");
    // IMPORTANT: Ensure we don't format dates to locale strings when getting raw messages
    const effectiveCoachName = coachName || ""; // Use empty string if undefined
    const messages = await getAllMessagesRaw(channelId, parse, effectiveCoachName, messageCount, messageTypes);
    console.log("[DECRYPT] Messages retrieved successfully");
    
    // Log a sample of dates to verify format
    if (parse && Array.isArray(messages) && messages.length > 0) {
      console.log("[DECRYPT] Sample message date format:", messages[0].Date);
      console.log("[DECRYPT] Sample message coach:", messages[0].CoachName);
      
      // Log unique coaches found in messages
      const coaches = [...new Set(messages.map(m => m.CoachName || 'unknown'))];
      console.log("[DECRYPT] Coaches found in messages:", coaches);
    }
    
    return messages;
  } catch (error) {
    console.error("[DECRYPT] Error fetching messages:", error);
    return parse ? [] : "[]";
  }
};

export const generateEnrolmentMessage = async (channelId: string) => {
  if(!(await verifyUserPermission(channelId))) return null;
  const coachId = "kokoro-enrolment-v1";

  // Execute all promises in parallel
  const [systemPromptData, profileData, messagesData] = await Promise.all([
    getCoachPrompt("Kokoro", "enrolmentPrompt"),
    getProfileText(channelId),
    decryptMessages(channelId, true, "all", 25, TEXTUAL_MSG_TYPES)
  ]);

  const { SystemPrompt } = systemPromptData;
  const { profileText } = profileData;

  const payload = {
    "system_instruction": {
      "parts": {
        "text": SystemPrompt
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": `<CLIENTS_PROFILE>${profileText}</CLIENTS_PROFILE><RECENT_MESSAGES>${messagesData}</RECENT_MESSAGES>`
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 1.0
    },
    "safetySettings": [
      {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_NONE"
      }
    ]
  }

  const response = await geminiRequest([payload]);
  const data = await response.json();
  return data.text1;
}

export const getStripePrices = async () => {
  try {
    console.log("[STRIPE] Fetching prices");
    const [pricesResponse, premiumCouponResponse, basicCouponResponse] = await Promise.all([
      // Fetch prices
      fetch("https://api.stripe.com/v1/prices", {
        headers: {
          Authorization: `Bearer ${Reframe.env.STRIPE_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      }),
      // Fetch premium coupon
      fetch(`https://api.stripe.com/v1/coupons/${Reframe.env.STRIPE_PREMIUM_DISCOUNT_COUPON_ID}`, {
        headers: {
          Authorization: `Bearer ${Reframe.env.STRIPE_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      }),
      // Fetch basic coupon
      fetch(`https://api.stripe.com/v1/coupons/${Reframe.env.STRIPE_BASIC_DISCOUNT_COUPON_ID}`, {
        headers: {
          Authorization: `Bearer ${Reframe.env.STRIPE_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      })
    ]);

    if (!pricesResponse.ok) {
      throw new Error(`Stripe API error fetching prices: ${pricesResponse.status}`);
    }

    const data = await pricesResponse.json();
    
    // Get discount percentages from coupons
    let premiumDiscountPercent = 0;
    let basicDiscountPercent = 0;
    
    if (premiumCouponResponse.ok) {
      const couponData = await premiumCouponResponse.json();
      if (couponData.percent_off) {
        premiumDiscountPercent = couponData.percent_off;
      }
    } else {
      console.warn("[STRIPE] Could not fetch premium coupon, using original prices");
    }
    
    if (basicCouponResponse.ok) {
      const couponData = await basicCouponResponse.json();
      if (couponData.percent_off) {
        basicDiscountPercent = couponData.percent_off;
      }
    } else {
      console.warn("[STRIPE] Could not fetch basic coupon, using original prices");
    }
    
    console.log("[STRIPE] Discount percentages:", { 
      premium: premiumDiscountPercent, 
      basic: basicDiscountPercent 
    });
    
    // Initialize the accumulator with the structure we need
    const initialAcc = {
      premium_plan: {
        price: { monthly: 0, yearly: 0 },
        originalPrice: { monthly: 0, yearly: 0 }
      },
      basic_plan: {
        price: { monthly: 0, yearly: 0 },
        originalPrice: { monthly: 0, yearly: 0 }
      }
    };
    
    // Filter active prices and organize them by plan and interval
    const prices = data.data.reduce((acc, price) => {
      if (!price.active) return acc;
      
      const interval = price.recurring?.interval === 'year' ? 'yearly' : 'monthly';
      let planType;

      if(price.id === Reframe.env.STRIPE_BASIC_MONTHLY_PRICE_ID) {
        planType = "basic_plan";
      }
      else if(price.id === Reframe.env.STRIPE_BASIC_YEARLY_PRICE_ID) {
        planType = "basic_plan";
      }
      else if(price.id === Reframe.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID) {
        planType = "premium_plan";
      }
      else if(price.id === Reframe.env.STRIPE_PREMIUM_YEARLY_PRICE_ID) {
        planType = "premium_plan";
      }

      console.log("[STRIPE] Processing price:", planType, interval, price.id, price.unit_amount);

      // Only proceed if we identified a valid plan type
      if (planType) {
        // Convert amount from cents to dollars
        const originalAmount = price.unit_amount / 100;
        
        // Store original price
        acc[planType].originalPrice[interval] = originalAmount;
        
        // Apply the appropriate discount based on plan type
        const discountPercent = planType === "premium_plan" ? premiumDiscountPercent : basicDiscountPercent;
        
        // Calculate discounted price if discount exists
        if (discountPercent > 0) {
          const discountedAmount = originalAmount * (1 - discountPercent / 100);
          acc[planType].price[interval] = Math.round(discountedAmount * 100) / 100; // Round to 2 decimal places
        } else {
          acc[planType].price[interval] = originalAmount;
        }
      }
      
      return acc;
    }, initialAcc);

    console.log("[STRIPE] Final prices with discounts:", prices);
    return prices;
  } catch (error) {
    console.error("[STRIPE] Error fetching prices:", error);
    throw error;
  }
};

export const checkCallLimit = async (channelId: string) => {
  console.log("[SERVER] Checking call limit for channelId:", channelId);
  
  try {
    // These functions should be imported from wherever they are defined
    const userData = await getUserData(channelId);
    const userPlanType = (userData.subscription?.planId.split('_')[0] ?? 'free') as PlanType;

    console.log("[SERVER] User plan type:", userPlanType);
    console.log("[SERVER] User essence balance:", await getTotalEssenceBalance(channelId));
    console.log("[SERVER] Action cost:", getActionCost("call", .5));
    if(getActionCost("call", .5) > await getTotalEssenceBalance(channelId)) {
      console.log("[SERVER] Monthly call limit reached for user:", channelId);
      return { 
        allowed: false, 
        error: "You've reached your monthly call limit",
        type: "monthly_call_limit_reached",
        action: userPlanType === "free" ? "checkout" : "top_up_xyz"
      };
    }

    // if (PLANS[userPlanType].limits.callCountPerDay <= userData.callCountToday) {
    //   console.log("[SERVER] Daily call limit reached for user:", channelId);
    //   return { 
    //     allowed: false, 
    //     error: "You've reached your daily call limit",
    //     type: "daily_call_limit_reached" 
    //   };
    // }

    return { allowed: true, maxCallDuration: PLANS[userPlanType].limits.maxCallDuration };
  } catch (error) {
    console.error("[SERVER] Error checking call limit:", error);
    return { 
      allowed: false,
      error: "Internal server error" 
    };
  }
};

/**
 * Manually generate a daily awakening message for a specific user
 * Useful for testing or for sending missed messages
 * 
 * @param channelId - The user's channel ID
 */
export const generateDailyAwakeningForUser = async (channelId: string) => {
  console.log("[MANUAL] Generating daily awakening for user:", channelId);
  
  try {
    // Get user data
    const user = await db
      .selectFrom("user")
      .select(["email", "timezone", "lastDailyAwakeningDate", "emailDaily"])
      .where("channel_id", "=", Number(channelId))
      .executeTakeFirst();
      
    if (!user || !user.timezone) {
      throw new Error("User not found or missing timezone");
    }
    
    // Check if user has opted out of daily emails
    if (user.emailDaily === 0) {
      console.log(`[MANUAL] User ${channelId} has opted out of daily emails. Skipping.`);
      return {
        success: false,
        skipped: true,
        error: "User has opted out of daily emails"
      };
    }
    
    // Get user profile and message history
    const [systemPromptData, profileData, messagesData] = await Promise.all([
      getCoachPrompt("Kokoro", "dailyAwakeningPrompt"),
      getProfileText(channelId),
      decryptMessages(channelId, true, "all", 25, TEXTUAL_MSG_TYPES)
    ]);
    
    // Add detailed logging to debug message structure
    console.log(`[MANUAL] Message data type:`, typeof messagesData);
    console.log(`[MANUAL] Is array:`, Array.isArray(messagesData));
    console.log(`[MANUAL] Message count:`, Array.isArray(messagesData) ? messagesData.length : 'N/A');
    
    if (Array.isArray(messagesData) && messagesData.length > 0) {
      console.log(`[MANUAL] First message sample:`, JSON.stringify(messagesData[0]));
      
      // Check message type property
      const messageTypeProperty = messagesData[0].Type !== undefined ? 'Type' : 
                                 messagesData[0].message_type !== undefined ? 'message_type' : 
                                 messagesData[0].MessageType !== undefined ? 'MessageType' : null;
      
      console.log(`[MANUAL] Message type property:`, messageTypeProperty);
    }
    
    // Check if user should receive awakening based on 1st, 3rd, 7th day schedule
    const awakeningCheck = shouldSendAwakeningOnSchedule(messagesData, channelId);
    
    if (!awakeningCheck.shouldSendAwakening) {
      console.log(`[MANUAL] User ${channelId} - ${awakeningCheck.reason}. Skipping.`);
      return {
        success: false,
        skipped: true,
        error: awakeningCheck.reason,
        daysSinceLastInteraction: awakeningCheck.daysSinceLastInteraction
      };
    }
    
    console.log(`[MANUAL] User ${channelId} - ${awakeningCheck.reason}. Proceeding with manual awakening.`);
    
    // Decrypt profile
    const { profileText } = profileData;
    
    // Prepare prompt for Claude
    const prompt = `
      <PROFILE>${profileText}</PROFILE>
      
      Identify one theme that will serve the client and create a daily morning message for them, including a simple action they can take. Keep it short.
    `;
    
    // Call Claude
    const messageArray = [
      {
        role: "user",
        content: [{ type: "text", text: prompt }]
      }
    ];

    const awakeningMessage = await claudeRequest(soulListenerSystemPrompt, messageArray);
    
    // Get the current date in user's timezone
    const now = new Date();
    const userTime = new Date(now.toLocaleString("en-US", { timeZone: user.timezone }));
    const userDateStr = userTime.toISOString().split('T')[0];
    
    // Save the daily awakening message
    await saveMessage(
      channelId, 
      "assistant", 
      awakeningMessage, 
      undefined, 
      "Default", 
      "daily_awakening"
    );
    
    // Update the last daily awakening date for this user
    await db
      .updateTable("user")
      .set({ lastDailyAwakeningDate: userDateStr })
      .where("channelId", "=", Number(channelId))
      .execute();
    
    console.log(`[MANUAL] Successfully generated daily awakening for user ${channelId} and updated last date to ${userDateStr}`);
    
    return { 
      success: true, 
      message: "Daily awakening generated successfully",
      content: awakeningMessage,
      date: userDateStr
    };
  } catch (error) {
    console.error("[MANUAL] Error generating daily awakening:", error);
    return { 
      success: false, 
      error: error.message || "Unknown error" 
    };
  }
};

/**
 * Process a VAPI transcript and save it to the conversation table
 * 
 * @param body - The request body containing channel and transcript data
 */
export async function processTranscriptAction(channel, transcript, coachNameOverride?: string) {
  try {
    if (!channel || !transcript || !Array.isArray(transcript)) {
      throw new Error("Invalid request: missing channel or transcript data");
    }

    console.log(`[TRANSCRIPT] Processing transcript for channel ${channel}, ${transcript.length} messages`);

    // 1) Determine the coach to attribute this transcript to
    // Prefer the coach provided by the call metadata; otherwise fall back to the user's selected coach
    let userCoachName = coachNameOverride as string | undefined;
    if (!userCoachName) {
      const userRecord = await db
        .selectFrom("user")
        .select(["selectedCoach"])
        .where("channelId", "=", Number(channel))
        .executeTakeFirst();
      // If no record or no selectedCoach, default to Kokoro (or any default you prefer)
      userCoachName = userRecord?.selectedCoach || "Kokoro";
    }
    const coachForTranscript: string = userCoachName || "Kokoro";
    
    // Filter & transform the transcript
    const processed = transcript
      .filter((t: any) => t.role !== "system")
      .map((t: any) => ({
        Sender: t.role === "bot" ? "assistant" : t.role,
        Date: t.time,
        Content: t.message,
        Channel: parseInt(channel, 10),
      }));
    
    console.log(`[TRANSCRIPT] Filtered to ${processed.length} messages`);
    
    // Encrypt and Save each message to the database, passing coachName
    for (const record of processed) {
      // Cleaning is now handled within saveMessage, no need to do it here
      // Only save if there's content (saveMessage will also check this after its own cleaning)
      if (record.Content && record.Content.trim()) {
        await saveMessage(
          record.Channel,
          record.Sender,
          record.Content, // Pass the raw content
          record.Date,
          "Default",
          MESSAGE_TYPES.CALL,
          coachForTranscript
        );
      }
    }
    
    console.log(`[TRANSCRIPT] Successfully saved all messages for channel ${channel}`);
    
    // Mark all assistant messages from the transcript as seen by user
    try {
      const { markMessagesSeenByUser } = await import("./actions/db/conversation-actions.ts");
      await markMessagesSeenByUser(channel, coachForTranscript);
      console.log(`[TRANSCRIPT] Marked messages as seen for channel ${channel}, coach ${coachForTranscript}`);
    } catch (error) {
      console.error("[TRANSCRIPT] Error marking messages as seen:", error);
    }
    
    return { success: true };
  } catch (error) {
    console.error("[TRANSCRIPT] Error processing transcript:", error);
    throw error;
  }
}

export const setFirstMessageFalse = async (channelId: string) => {
  if(!(await verifyUserPermission(channelId))) return null;
  console.log("Calling setfirstmessagefalse with id ", channelId);
  try {
    const user = await db
      .updateTable("user")
      .set({ firstMessage: false })
      .where("channel_id", "=", channelId)
      .returningAll()
      .execute();
    console.log("user", user);
    return {
      user,
      success: true,
    };
  } catch (error) {
    console.error("Error setting first message to false:", error);
    throw error;
  }
}

export const getSummaryInfo = async (channelId: string) => {
  if(!(await verifyUserPermission(channelId))) return null;

  console.log("Calling getsummaryinfo with channelid ", channelId);
  const summary = await db
    .selectFrom("summary")
    .selectAll()
    .where("channel_id", "=", channelId)
    .where("summary_sent", "=", true)
    .execute();
  console.log("summary", summary);

  if (summary.length > 0) {
    return {
      summary,
      success: true,
    };
  } else {
    return {
      success: false,
    };
  }
}

export const waitForCallProcessingComplete = async (callId: string, channelId: string) => {
  console.log("[LONG POLL] Starting long poll for call processing completion, callId:", callId);
  
  const maxWaitTime = 60000; // 60 seconds timeout
  const pollInterval = 1000; // Check every 1 second
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check if call processing is complete
      const callDetails = await db
        .selectFrom("callDetails")
        .selectAll()
        .where("call_id", "=", callId)
        .where("status", "=", "complete")
        .execute();
      
      if (callDetails.length > 0) {
        console.log("[LONG POLL] Call processing complete, fetching messages");
        
        const callDetail = callDetails[0];
        
        // Get the latest messages
        const messages = await getAllMessages(channelId, 50);
        
        return {
          success: true,
          messages,
          callDetail
        };
      }
      
      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      
    } catch (error) {
      console.error("[LONG POLL] Error during wait:", error);
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }
  
  console.log("[LONG POLL] Timeout reached, call processing may have failed");
  return {
    success: false,
    error: "Timeout waiting for call processing"
  };
}

const onboardUser = async (user: {
  email: string;
  channelId: number;
  name: string;
  image?: string;
  firstAnswer?: string;
  secondAnswer?: string;
  thirdAnswer?: string;
}) => {
  console.log("user NOT in make ", user);

  // replace new lines and quotes
  const profileText = `
    Name: ${user.name}
    User ID: ${user.channelId}

    Baseline State of Being:

    Level of Self-Awareness:
    Openness to Change: 
    Preferred Learning Style:

    Spirituality/Faith: ${user.secondAnswer}

    Personal details: 

    Immediate Goals: ${user.thirdAnswer}

    Deeper Goals: ${user.firstAnswer}
  `.replace(/\n/g, "\\n").replace(/"/g, "\\\"");

  // status, agentName, agentId, coachId, coachName, coachMode

  const status = "active";
  const profileInitiated = false;
  const messagesTillProfileUpdate = 15;
  const agentName = `${user.channelId} DM Kokoro Sonnet-3.5-new Principles v4 + Yang`;
  const agentId = `https://exponentially.app.n8n.cloud/webhook/intuitive-omni-jp-v4-3-gemini-stages-dev-wip`;
  const coachId = 'principles-yang-call-v4';
  const coachName = 'principles-yang-call-v4';
  const coachMode = 'normal';

  const metaFeedback = `- Keep it simple: This is a new client. You don't know their reality and what they mean yet. Slow down, listen, ask ONE simple question, like 'Go on', or 'What's important about that?', 'Say more', 'I see, what do you mean by X?'. Explore and understand their current experience first. (KOKOROS_CORE_PRINCIPLES #8 - SLOW DOWN).

- Get specific: Yes, you coach the being not the problem. BUT the client's story of the problem will reveal their being. Instead of generalities, guide them to get specific. Ask a simple SINGLE question to explore concrete examples of how they are already living this and what it means to them (KOKOROS_CORE_PRINCIPLES #4 - Surface the obstacle BEFORE transcending it) 

- Listen: After posing a potent inquiry, resist the urge to layer on additional questions or prompts. Trust the transformative power of ONE question and allow the client to marinate in it. Offer them as much space as they need to process and respond (KOKOROS_CORE_PRINCIPLES #3 - BREVITY is power)
  `;

  const messagesTillFeedback = 4;

  // update user db for channelId

  await db
    .updateTable("user")
    .set({
      status,
      agentName,
      agentId,
      coachId,
      coachName,
      coachMode,
      profileText,
      profileInitiated,
      messagesTillProfileUpdate,
      metaFeedback,
      messagesTillFeedback,
    })
    .where("channelId", "=", user.channelId)
    .executeTakeFirst();

  await setProfileText(user.channelId.toString(), profileText);


  console.log("EVERYTHING UP TO HERE WORKED!!!");
};

export const completeOnboarding = async (channelId: string, questionAnswer: any) => {
  try {
    console.log("[ONBOARDING] Starting onboarding completion with data:", {
      channelId,
      questionAnswer
    });

    if (!channelId) {
      throw new Error("Missing channelId");
    }

    // First check if user exists
    const existingUser = await db
      .selectFrom("user")
      .selectAll()
      .where("channel_id", "=", channelId)
      .executeTakeFirst();

    console.log("[ONBOARDING] Found existing user:", existingUser);

    if (!existingUser) {
      throw new Error(`No user found with channelId: ${channelId}`);
    }

    // Update user onboarding status
    const updatedUser = await db
      .updateTable("user")
      .set({ onboarding: 1 })
      .where("channel_id", "=", channelId)
      .returningAll()
      .execute();

    console.log("[ONBOARDING] Updated user:", updatedUser);

    const firstAnswer = questionAnswer["1"].join(", ");
    const secondAnswer = questionAnswer["2"].join(", ");
    const thirdAnswer = questionAnswer["3"].join(", ");

    console.log("[ONBOARDING] Formatted answers:", {
      firstAnswer,
      secondAnswer,
      thirdAnswer,
    });

    const newUserResponse = await onboardUser({
      ...updatedUser[0],
      channelId,
      firstAnswer,
      secondAnswer,
      thirdAnswer,
    });

    console.log("[ONBOARDING] Make integration response:", newUserResponse);

    return {
      user: updatedUser[0],
      success: true,
    };

  } catch (error) {
    console.error("[ONBOARDING] Error:", {
      message: error.message,
      stack: error.stack,
    });

    return {
      error: error.message,
      success: false,
    };
  }
}

export const integrationUpdate = async (tableName: string, recordId: string, newContent: string) => {
  try {
    // example tableName: "Awakenings", "Practices", "Commitments", "awakening_notes"
    // The content field is "Content"
    if (!recordId || !tableName) {
      return { success: false, error: "missing data" };
    }

    const patchUrl = `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/${encodeURIComponent(tableName)}`;
    const patchData = {
      records: [
        {
          id: recordId,
          fields: {
            content: newContent,
          },
        },
      ],
    };
    const updateResponse = await fetch(patchUrl, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(patchData),
    });
    if (!updateResponse.ok) {
      const err = await updateResponse.text();
      throw new Error(err);
    }
    return { success: true };
  } catch (error) {
    console.error("Error updating integration content:", error);
    return { success: false, error: error.message } ;
  }
}

export const getUsage = async (channelId: string) => {
  try {
    const userData = await getUserData(channelId);
    const userPlanType = (userData.subscription?.planId.split('_')[0] ?? 'free') as PlanType;
    return { 
      userData,
      plan: PLANS[userPlanType]
    };
  } catch (error) {
    console.error("Error getting usage:", error);
    return { error: error.message };
  }
}

export const encryptProfile = (profile: string) => {
  const secretKey = Reframe.env.SECRET_KEY;

  const encryptedProfile = CryptoJS.AES.encrypt(
    profile,
    secretKey
  ).toString();
  return encryptedProfile;
}

export const saveMetafeedback = async (channelId: string, metafeedback: string, coachName: string) => {
  try {
    await db
      .updateTable("user_coach_attributes")
      .set({ meta_feedback: metafeedback })
      .where("channel_id", "=", Number(channelId))
      .where("coach_name", "=", coachName)
      .execute();
    return { success: true };
  } catch (error) {
    console.error("Error saving metafeedback:", error);
    return { success: false, error: error.message };
  }
}

export const getMetaFeedback = async (channelId: string, coachName: string) => {
  if (!METAFEEDBACK_ENABLED) {
    return { metaFeedback: "None right now." };
  }
  
  // Get coach-specific data
  const coachAttrs = await db
    .selectFrom("user_coach_attributes")
    .select(["meta_feedback", "messages_till_feedback"])
    .where("channel_id", "=", Number(channelId))
    .where("coach_name", "=", coachName)
    .executeTakeFirst();
  

  if (!coachAttrs || !coachAttrs.meta_feedback) {
    console.log("[METAFEEDBACK_DEBUG] No coachAttrs or meta_feedback");
    return { metaFeedback: "None right now." };
  }
  
  // Show feedback intermittently based on coach-specific counter
  // Shows when counter is at 2, 3, 6, 7 (out of 8-count cycle) = ~50% of the time
  const showFeedback = coachAttrs.messagesTillFeedback % 4 >= 2;
  
  if (!showFeedback) {
    console.log("[METAFEEDBACK_DEBUG] Not showing feedback due to counter logic");
    return { metaFeedback: "None right now." };
  }
  
  const metaFeedback = coachAttrs.meta_feedback.replace(/\\n/g, "\n").replace(/\\"/g, '"');
  console.log("[METAFEEDBACK_DEBUG] Returning feedback, length:", metaFeedback.length);
  return { metaFeedback };
}

const getProfileAndTranscript = async (channelId: string) => {
  const [profileData, messagesData] = await Promise.all([
    getProfileText(channelId),
    decryptMessages(channelId, true, "all", 25, TEXTUAL_MSG_TYPES)
  ]);

  const { profileText } = profileData;
  const decryptedMessages = messagesData;

  const transcript = decryptedMessages
    .sort((a, b) => new Date(a.Date).getTime() - new Date(b.Date).getTime())
    .map(msg => `${msg.Sender}: ${msg.Content}`)
    .join("\n");

  return { profileText, transcript }
}

export const initiateProfile = async (channelId: string, vapiTranscript: string | null = null) => {
  await initiateUserProfile(channelId);
  const { profileText, transcript: transcriptText } = await getProfileAndTranscript(channelId);

  const transcript = vapiTranscript ? vapiTranscript : transcriptText;

  let combinedText =
    `<transcript>
    ${transcript}
    </transcript>

    <STARTING CLIENT PROFILE>
    ${profileText}
    </STARTING CLIENT PROFILE>`;

  const systemPrompt = initPrompt;
  const messageArray = [
    {
      role: "user",
      content: combinedText
    }
  ]

  const responseText = await claudeRequest(systemPrompt, messageArray);
  const regex = /<updated_profile>\s*([\s\S]*?)\s*<\/updated_profile>/;
  const match = responseText.match(regex);

  if(!match || !match[1]) return;

  const updatedProfileText = match[1].trim();

  await setProfileText(channelId.toString(), updatedProfileText);
}

export const updateProfileText = async (channelId: string, vapiTranscript: string | null = null) => {
  console.log("[UPDATE PROFILE TEXT] CALLED")
  await db.updateTable("user").set({ messagesTillProfileUpdate: 15 }).where("channelId", "=", Number(channelId)).execute();
  
  const { profileText, transcript: transcriptText } = await getProfileAndTranscript(channelId);
  const transcript = vapiTranscript ? vapiTranscript : transcriptText;
  
  let combinedText =
  `<CLIENT PROFILE>
  ${profileText}
  </CLIENT PROFILE>

  <RECENT CONVERSATIONS>
  ${transcript}
  </RECENT CONVERSATIONS>`;

  let payload = {
    "system_instruction": {
      "parts": {
        "text": updateInsightPrompt
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": combinedText
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 1.0
    },
    "safetySettings": [
      {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_NONE"
      }
    ]
  }

  console.log("[UPDATE_PROFILE_TEXT] First Gemini payload:", JSON.stringify(payload, null, 2));
  let response = await geminiRequest([payload]);
  let data = await response.json();
  let responseText = data.text1;
  console.log("[UPDATE_PROFILE_TEXT] First Gemini response (insights):", responseText);

  const regex = /<INSIGHTS AND COMMITMENTS>\s*([\s\S]*?)\s*<\/INSIGHTS AND COMMITMENTS>/;
  const match = responseText.match(regex);

  if(!match || !match[1]) return;

  const insightText = match[1].trim();

  combinedText =
  `<CURRENT CLIENT PROFILE>
  ${profileText}
  </CURRENT CLIENT PROFILE>

  <RECENT CONVERSATIONS>
  ${transcript}
  </RECENT CONVERSATIONS>

  <DRAFT NOTES>
  ${insightText}
  </DRAFT NOTES>`;

  payload = {
    "system_instruction": {
      "parts": {
        "text": updateProfilePrompt
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": combinedText
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 1.0
    },
    "safetySettings": [
      {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_NONE"
      }
    ]
  }

  response = await geminiRequest([payload]);
  data = await response.json();
  responseText = data.text1;
  console.log("[UPDATE_PROFILE_TEXT] Second Gemini response (final profile):", responseText);

  await setProfileText(channelId.toString(), responseText);
  console.log("[UPDATE PROFILE TEXT] DONE")
}

export const updateMetaFeedback = async (channelId: string, coachName: string) => {
  console.log("[METAFEEDBACK] updateMetaFeedback", { channelId, coachName });
  try {
    /* ------------------------------------------------------------------
       1. RESET COUNTER FOR THIS COACH
    ------------------------------------------------------------------ */
    await db
      .updateTable("user_coach_attributes")
      .set({ messages_till_feedback: 8 })
      .where("channel_id", "=", Number(channelId))
      .where("coach_name", "=", coachName)
      .execute();

    /* ------------------------------------------------------------------
       2. FETCH PROMPT & CONTEXT
    ------------------------------------------------------------------ */
    const { SystemPrompt } = await getCoachPrompt(coachName, "metaFeedbackPrompt");
    const { profileText, transcript } = await getProfileAndTranscript(channelId);
    const systemPrompt = (SystemPrompt || "").replace("{{CLIENT_PROFILE}}", profileText || "");

    const combinedText = `\n<TRANSCRIPT>\n${transcript}\n</TRANSCRIPT>`;

    /* ------------------------------------------------------------------
       3. RESOLVE MODEL FROM COACH METADATA
    ------------------------------------------------------------------ */
    let modelProvider: "anthropic" | "google" | "openrouter" = "anthropic";
    let modelName = "claude-3-5-sonnet-20241022";

    try {
      const coach = await getCoachAction(coachName, channelId);
      if (coach?.metadata) {
        const md = typeof coach.metadata === "string" ? JSON.parse(coach.metadata) : coach.metadata;
        const cfg = md?.messageModel;
        if (cfg?.model) {
          modelProvider = (cfg.provider || "anthropic").toLowerCase();
          modelName = cfg.model;
        }
      }
    } catch (err) {
      console.warn("[METAFEEDBACK] Could not resolve coach model, falling back to defaults", err);
    }

    /* ------------------------------------------------------------------
       4. CALL THE SELECTED LLM
    ------------------------------------------------------------------ */
    let feedback: string = "";

    if (modelProvider === "google") {
      const payload = {
        system_instruction: { parts: { text: systemPrompt } },
        contents: [
          { role: "user", parts: [{ text: combinedText }] }
        ],
        generationConfig: { temperature: 1.0 },
        safetySettings: [
          { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
          { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" }
        ]
      };
      const resp = await geminiRequest([payload], modelName);
      const data = await resp.json();
      feedback = data.text1 || "";
    } else if (modelProvider === "openrouter") {
      feedback = await openRouterRequest(modelName, systemPrompt, combinedText);
    } else {
      // Anthropic Claude
      const assistantTurn = await claudeRequestRaw({
        system: systemPrompt,
        messages: [{ role: "user", content: combinedText }],
        model: modelName,
        temperature: 0.9,
        max_tokens: 1800
      });
      feedback = assistantTurn?.content?.[0]?.text || "";
    }

    /* ------------------------------------------------------------------
       5. EXTRACT <FEEDBACK_TO_COACH> …
    ------------------------------------------------------------------ */
    const regex = /<FEEDBACK_TO_COACH>\s*([\s\S]*?)\s*<\/FEEDBACK_TO_COACH>/;
    const match = feedback.match(regex);
    if (!match || !match[1]) {
      console.log("[METAFEEDBACK] No valid feedback found. First 200 chars:", feedback.substring(0, 200));
      return;
    }

    const metaFeedback = match[1].trim();
    await saveMetafeedback(channelId, metaFeedback, coachName);
    console.log("[METAFEEDBACK] Saved feedback, length:", metaFeedback.length);
  } catch (error) {
    console.error("[METAFEEDBACK] Error in updateMetaFeedback:", error);
    throw error;
  }
}

export const updateProfileIfNeeded = async (channelId: string, coachName: string) => {
  console.log("[METAFEEDBACK_DEBUG] updateProfileIfNeeded called with:", { channelId, coachName });
  
  // Get initial counter value for logging
  const initialCoachAttrs = await db.selectFrom("user_coach_attributes")
    .select(["messages_till_feedback"])
    .where("channel_id", "=", Number(channelId))
    .where("coach_name", "=", coachName)
    .executeTakeFirst();
  console.log("[METAFEEDBACK_DEBUG] Initial messages_till_feedback:", initialCoachAttrs?.messagesTillFeedback);
  
  // Update profile counter (stays global for now)
  await db.updateTable("user").set({ 
    messagesTillProfileUpdate: sql`MAX(messages_till_profile_update - 1, 0)`
  }).where("channelId", "=", Number(channelId)).execute();
  
  // Update coach-specific feedback counter
  await db.updateTable("user_coach_attributes").set({ 
    messages_till_feedback: sql`MAX(messages_till_feedback - 1, 0)`
  })
  .where("channel_id", "=", Number(channelId))
  .where("coach_name", "=", coachName)
  .execute();
  
  // Check profile update (stays global)
  const user = await db.selectFrom("user")
    .select(["profileInitiated", "totalMessages", "messagesTillProfileUpdate"])
    .where("channelId", "=", Number(channelId))
    .executeTakeFirst();
  
  // Check coach-specific feedback update
  const coachAttrs = await db.selectFrom("user_coach_attributes")
    .select(["messages_till_feedback"])
    .where("channel_id", "=", Number(channelId))
    .where("coach_name", "=", coachName)
    .executeTakeFirst();
  
  console.log("[METAFEEDBACK_DEBUG] After decrement - coachAttrs:", coachAttrs);
  console.log("[METAFEEDBACK_DEBUG] METAFEEDBACK_ENABLED:", METAFEEDBACK_ENABLED);
  
  // Handle profile updates (unchanged)
  if(!user.profileInitiated && user.totalMessages >= 8) {
    await initiateProfile(channelId);
  } else if(user.messagesTillProfileUpdate <= 0) {
    await updateProfileText(channelId);
  }
  
  // Handle coach-specific metafeedback
  if (METAFEEDBACK_ENABLED && coachAttrs?.messagesTillFeedback <= 0) {
    console.log("[METAFEEDBACK_DEBUG] Triggering updateMetaFeedback - counter is:", coachAttrs.messagesTillFeedback);
    await updateMetaFeedback(channelId, coachName);
  } else {
    console.log("[METAFEEDBACK_DEBUG] NOT triggering updateMetaFeedback - conditions not met");
  }
}