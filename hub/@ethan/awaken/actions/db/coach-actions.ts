"use server";

import { db } from "../../lib/db.ts";
import { sql } from "npm:kysely";
import { v4 as uuidv4 } from "npm:uuid";
import { verifyUserPermission, checkIsSuperUser } from "../../lib/auth-helper.ts";
import { SUPERUSER_EMAILS, BETA_USER_EMAILS } from "../../lib/server-constants.ts";
import { updateSelectedCoach } from "./user-actions.ts";
import { getActionCost } from "../../lib/user-actions.ts";
import { essenceCost } from "../../lib/essence.ts";

// Add necessary type definitions for cards based on the plan
// Note: These might need adjustment based on actual DB schema and Kysely codegen
interface CardDefinition {
  id: string; // UUID
  type: string;
  default_title: string;
  default_description: string | null;
  default_ui_config: Record<string, any>; // JSONB
}

interface CoachCardOffering {
  id: string; // UUID
  coach_name: string;
  card_definition_id: string; // UUID
  type: string; // Redundant type
  title_override: string | null;
  description_override: string | null;
  ui_config_override: Record<string, any> | null; // JSONB
  sort_order: number;
  data: Record<string, any>; // JSONB
  requires_beta_access: boolean | number | null; // Add the new flag
}

interface UserCard {
  id: string; // UUID
  user_channel_id: number;
  coach_offering_id: string; // UUID
  type: string; // Redundant type
  title_override: string | null;
  description_override: string | null;
  data: Record<string, any>; // JSONB, instance data like sessionPlan
  status: string;
  is_visible: boolean;
  sort_order: number;
  last_used_at: Date | null;
}

// The final view model returned to the client
export interface UserCardView {
  user_card_id: string | null; // ID from user_cards if it exists
  coach_offering_id: string; // ID from coach_card_offerings
  type: string;
  effectiveTitle: string;
  effectiveDescription: string | null;
  uiConfig: Record<string, any>; // Merged UI config
  instance_data: Record<string, any>; // Data from user_cards
  instance_status: string; // Status from user_cards, or default
  sort_order: number; // Final sort order
  goalInput?: string | Uint8Array;
}

// Helper function to merge UI configs (simple deep merge for now)
// TODO: Implement more sophisticated merging if needed (e.g., handling arrays)
function mergeUiConfigs(...configs: (Record<string, any> | null | undefined)[]): Record<string, any> {
  let merged: Record<string, any> = {};
  for (const config of configs) {
    if (config) {
      for (const key in config) {
        if (Object.prototype.hasOwnProperty.call(config, key)) {
          const value = config[key];
          if (typeof value === 'object' && value !== null && !Array.isArray(value) && typeof merged[key] === 'object' && merged[key] !== null && !Array.isArray(merged[key])) {
            merged[key] = mergeUiConfigs(merged[key], value);
          } else {
            merged[key] = value;
          }
        }
      }
    }
  }
  return merged;
}

/**
 * Checks if a user has beta access based on email list.
 * 
 * @param channelId - The channel ID of the user.
 * @returns Promise<boolean> - True if the user has beta access, false otherwise.
 */
export const checkBetaAccess = async (
  channelId: number
): Promise<boolean> => {
  try {
    const user = await db
      .selectFrom("user")
      .select(["email"])
      .where("channelId", "=", channelId)
      .executeTakeFirst();

    if (!user?.email) {
      console.log(`[ACCESS_CHECK] No email found for channelId ${channelId}, denying beta access.`);
      return false;
    }

    const normalizedUserEmail = user.email.toLowerCase().trim();
    const lowerCaseBetaEmails = BETA_USER_EMAILS.map((e) =>
      e.toLowerCase().trim()
    );

    const hasAccess = lowerCaseBetaEmails.includes(normalizedUserEmail);

    console.log(
      `[ACCESS_CHECK] Beta access for ${user.email} (normalized: ${normalizedUserEmail}): ${hasAccess}`
    );
    return hasAccess;
  } catch (error) {
    console.error(`[ACCESS_CHECK] Error checking beta access for channelId ${channelId}:`, error);
    return false; // Default to no access on error
  }
}; 

/**
 * CURRENTLY UNUSED
 * Gets a coach by name, or the default coach if no name is provided
 * Also includes the user's selected voice for this coach if channelId is provided
 * 
 * @param coachName - Optional coach name to get
 * @param channelId - Optional channel ID to get user's voice preference
 * @returns Promise<Coach | null> - The coach object with user's voice preference, or null if not found
 */
export const getCoachAction = async (coachName?: string, channelId?: string) => {
  if(!(await verifyUserPermission(channelId))) return null;
  try {
    console.log("[COACH] Getting coach with name:", coachName || "default");
    
    // If no coachName provided, get the default coach
    if (!coachName) {
      const defaultCoachName = await getDefaultCoachAction();
      if (!defaultCoachName) {
        console.log("[COACH] No default coach found");
        return null;
      }
      coachName = defaultCoachName;
    }
    
    // Get the coach by name
    const coach = await db
      .selectFrom("coaches")
      .selectAll()
      .where("name", "=", coachName)
      .executeTakeFirst();

    if (!coach) {
      console.log("[COACH] No coach found with name:", coachName);
      return null;
    }

    // Add cost multiplier to the coach object
    coach.costMultiplier = Number(coach.cost_multiplier);

    console.log(`[COACH] Found coach: ${coach.name}, ID: ${coach.id}`);
    
    // Parse coach metadata to check available voices
    let availableVoices: any[] = [];
    let metadata: any;
    
    if (coach.metadata) {
      metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      availableVoices = metadata.voices || [];
      if (availableVoices.length > 0) {
        console.log(`[COACH VOICES] Available voices:`, 
          availableVoices.map((v) => v.voice_name).join(', '));
      }
    }

    // If channelId is provided, get the user's voice preference
    let selectedVoiceName;
    if (channelId) {
      const user = await db
        .selectFrom("user")
        .select(["selectedVoices"])
        .where("channelId", "=", Number(channelId))
        .executeTakeFirst();
      
      if (user?.selectedVoices) {
        try {
          // Parse if string, otherwise use as-is
          const selectedVoices = typeof user.selectedVoices === 'string'
            ? JSON.parse(user.selectedVoices)
            : user.selectedVoices;
          
          selectedVoiceName = selectedVoices[coachName];
          console.log(`[COACH VOICES] Selected voice for ${coachName}: ${selectedVoiceName || 'None'}`);
        } catch (error) {
          console.error(`[COACH VOICES] Error parsing selected voices:`, error);
        }
      }
    }
    
    // Find selected voice details if we have a selection
    if (selectedVoiceName && availableVoices.length > 0) {
      const selectedVoice = availableVoices.find((voice: any) => 
        voice.voice_name === selectedVoiceName);
      
      if (selectedVoice) {
        console.log(`[COACH VOICES] Using voice: ${selectedVoice.voice_name} (${selectedVoice.voice_id})`);
        coach.selectedVoice = selectedVoice;
      }
    }
    
    // If no selected voice was found, use the first available voice
    if (!coach.selectedVoice && availableVoices.length > 0) {
      coach.selectedVoice = availableVoices[0];
      console.log(`[COACH VOICES] Using default voice: ${coach.selectedVoice.voice_name}`);
    }

    return coach;
  } catch (error) {
    console.error("[COACH] Error getting coach:", error);
    return null;
  }
};

/**
 * Gets the default coach from the coaches table
 * 
 * @returns Promise<string | null> - The name of the default coach, or null if not found
 */
export const getDefaultCoachAction = async (): Promise<string | null> => {
  try {
    const defaultCoach = await db
      .selectFrom("coaches")
      .select(["name"])
      .where("default_coach", "=", true)
      .executeTakeFirst();

    return defaultCoach?.name || "Kokoro";
  } catch (error) {
    console.error("[COACH_ACCESS] Error getting default coach:", error);
    return null;
  }
};

/**
 * Checks if a user has access to a specific coach
 * Access can come from either community membership (user.coach_access) 
 * or subscription plan (subscription.coach_access)
 * 
 * @param channelId - The user's channel ID
 * @param coachName - The coach name to check access for
 * @returns Promise<boolean> - Whether the user has access to the coach
 */
export const userHasCoachAccessAction = async (channelId: string, coachName: string): Promise<boolean> => {
  try {
    console.log("[COACH_ACCESS] Checking access for coach:", coachName, "for user:", channelId);
    
    // Get user and their active subscription
    const user = await db
      .selectFrom("user")
      .select(["coachAccess", "stripeSubscriptionId"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      console.log("[COACH_ACCESS] No user found for channelId:", channelId);
      return false;
    }

    console.log("[COACH_ACCESS] User data:", user);
    console.log("[COACH_ACCESS] Raw coach_access:", user.coachAccess);

    // Parse the JSON string if it's a string
    const userCoachAccess = typeof user.coachAccess === 'string' 
      ? JSON.parse(user.coachAccess)
      : (user.coachAccess as Record<string, boolean> || {});

    console.log("[COACH_ACCESS] Parsed coach_access:", userCoachAccess);
    
    const fromCommunity = !!userCoachAccess[coachName];
    console.log("[COACH_ACCESS] Access check result:", fromCommunity);
    
    if (fromCommunity) {
      console.log("[COACH_ACCESS] User has community-based access to coach:", coachName);
      return true;
    }

    // If user has an active subscription, check if it grants access to the coach
    if (user.stripeSubscriptionId) {
      const subscription = await db
        .selectFrom("subscription")
        .select(["coach_access"])
        .where("id", "=", user.stripeSubscriptionId)
        .where("status", "=", "active")
        .executeTakeFirst();

      if (subscription) {
        // Parse the JSON string if it's a string
        const subscriptionCoachAccess = typeof subscription.coach_access === 'string'
          ? JSON.parse(subscription.coach_access)
          : (subscription.coach_access as Record<string, boolean> || {});
        
        const fromSubscription = !!subscriptionCoachAccess[coachName];
        
        if (fromSubscription) {
          console.log("[COACH_ACCESS] User has subscription-based access to coach:", coachName);
          return true;
        }
      }
    }

    console.log("[COACH_ACCESS] User does not have access to coach:", coachName);
    return false;
  } catch (error) {
    console.error("[COACH_ACCESS] Error checking coach access:", error);
    return false;
  }
};

/**
 * Fetches and merges card data for a specific user and coach.
 * Combines coach offerings with user-specific card instances.
 *
 * @param userEmail - The user's email address (string).
 * @param channelId - The user's channel ID (number).
 * @param coachName - The name of the coach.
 * @returns Promise<UserCardView[]> - An array of card views ready for the frontend.
 */
export const getUserCardsForCoachAction = async (channelId: number, coachName: string): Promise<UserCardView[]> => {
  if(!(await verifyUserPermission(channelId.toString()))) return [];
  console.log(`[CARDS] Fetching cards for user channel ${channelId}, coach ${coachName}`);
  try {
    // === Step 1: Fetch User and Check Access Levels ===
    const user = await db
      .selectFrom("user")
      .select(["email"]) // Only need email for superuser check
      .where("channelId", "=", channelId)
      .executeTakeFirst();

    // User check might be redundant if channelId implies user exists, but good practice
    if (!user) {
      console.warn(`[CARDS] No user found for channel ID: ${channelId}. Cannot check superuser status.`);
      return []; // Or throw an error, depending on expected behavior
    }

    const hasBetaAccess = await checkBetaAccess(channelId);
    const isSuper = user.email ? await checkIsSuperUser(user.email) : false;
    console.log(`[CARDS_ACCESS] User: ${user.email}, Beta Access: ${hasBetaAccess}, Superuser: ${isSuper}`);

    // === Step 2: Fetch ALL Coach Offerings (including hidden ones) ===
    const allOfferingsRaw = await db
      .selectFrom("coach_card_offerings as cco")
      .innerJoin("card_definitions as cd", "cco.card_definition_id", "cd.id")
      .select([
        "cco.id as coachOfferingId",
        "cco.type as type",
        "cco.sort_order as offeringSortOrder",
        sql<string>`COALESCE(cco.title_override, cd.default_title)`.as("baseTitle"),
        sql<string | null>`COALESCE(cco.description_override, cd.default_description)`.as("baseDescription"),
        "cd.default_ui_config as defaultUiConfig",
        "cco.ui_config_override as uiConfigOverride",
        "cco.data as offeringData",
        "cd.requires_user_instance as requiresUserInstance",
        "cco.requires_beta_access as requiresBetaAccess",
        "cco.is_visible as isVisible"
      ])
      .where("cco.coach_name", "=", coachName)
      // REMOVED: .where("cco.is_visible", "=", 1) - Fetch all initially
      .orderBy("cco.sort_order", "asc")
      .execute();
    console.log(`[CARDS] Found ${allOfferingsRaw.length} total coach offerings (visible and hidden) for ${coachName}`);

    // === Step 3: Filter offerings based on beta access AND superuser status ===
    const accessibleOfferings = allOfferingsRaw.filter(offering => {
      const needsBeta = !!offering.requiresBetaAccess;
      
      // Determine if the user should see this offering
      const shouldInclude = !needsBeta || hasBetaAccess || isSuper;

      // Optional: Log why something is included or excluded
      if (needsBeta && !shouldInclude) { 
          console.log(`[CARDS_FILTER] Excluding Beta Offering: ${offering.coachOfferingId} (Type: ${offering.type})`);
      } else if (needsBeta && isSuper && !hasBetaAccess) {
          console.log(`[CARDS_FILTER] Including Beta Offering ${offering.coachOfferingId} due to Superuser override.`);
      }

      return shouldInclude;
    });
    console.log(`[CARDS] ${accessibleOfferings.length} offerings are accessible after beta/super user check.`);

    // Define the shape of the offering query result based on aliases
    type OfferingQueryResult = {
      coachOfferingId: string;
      type: string;
      offeringSortOrder: number;
      baseTitle: string;
      baseDescription: string | null;
      // Allow for string type if DB returns JSON as string before parsing
      defaultUiConfig: Record<string, any> | string;
      uiConfigOverride: Record<string, any> | string | null;
      offeringData: Record<string, any> | string;
      requiresUserInstance: number;
      requiresBetaAccess: boolean | number | null;
      isVisible: number;
    };

    // Create a map of ALL offerings for quick lookup when processing user cards
    const allOfferingMap = new Map<string, OfferingQueryResult>(
      allOfferingsRaw.map(o => [o.coachOfferingId, o as OfferingQueryResult])
    );

    // === Step 4: Fetch User-Specific Cards (aliased to camelCase) ===
    const userCards = await db
      .selectFrom("user_cards as uc")
      .innerJoin("coach_card_offerings as cco", "uc.coach_offering_id", "cco.id")
      .select([
        "uc.id as userCardId",
        "uc.coach_offering_id as coachOfferingId", 
        "uc.type as type",
        "uc.title_override as instanceTitleOverride",
        "uc.description_override as instanceDescriptionOverride",
        "uc.data as instanceData",
        "uc.status as instanceStatus",
        "uc.sort_order as userSortOrder",
      ])
      .where("uc.user_channel_id", "=", channelId)
      .where("cco.coach_name", "=", coachName)
      .where("uc.is_visible", "=", 1)
      .orderBy("uc.sort_order", "asc")
      .execute();
    console.log(`[CARDS] Found ${userCards.length} user-specific cards for user channel ${channelId}`);

    // 3. Merge Logic - Prioritize User Cards
    const finalCardViews: UserCardView[] = [];
    const processedOfferingIds = new Set<string>();

    // Iterate through user cards first
    for (const userCard of userCards) {
      // Look up in the map of ALL offerings
      const offering = allOfferingMap.get(userCard.coachOfferingId);

      if (offering) {
        console.log(`[CARDS_USER] Processing user card ${userCard.userCardId} (Offering: ${userCard.coachOfferingId})`);
        // Mark this offering ID as processed
        processedOfferingIds.add(userCard.coachOfferingId);

        // Parse JSON fields from offering and userCard
        const defaultUiConfig = typeof offering.defaultUiConfig === 'string' ? JSON.parse(offering.defaultUiConfig) : offering.defaultUiConfig;
        const offeringUiConfigOverride = typeof offering.uiConfigOverride === 'string' ? JSON.parse(offering.uiConfigOverride) : offering.uiConfigOverride;
        const offeringDataParsed = typeof offering.offeringData === 'string' ? JSON.parse(offering.offeringData) : (offering.offeringData || {});
        // Parse instanceData if it's a string, otherwise use the object or default to empty
        const userCardInstanceData = typeof userCard.instanceData === 'string'
            ? JSON.parse(userCard.instanceData)
            : (userCard.instanceData || {});

        // Merge UI configs
        const mergedUiConfig = mergeUiConfigs(defaultUiConfig, offeringUiConfigOverride);

        // Create view based on user card data
        const cardView: UserCardView = {
          user_card_id: userCard.userCardId,
          coach_offering_id: userCard.coachOfferingId,
          type: userCard.type,
          effectiveTitle: userCard.instanceTitleOverride ?? offering.baseTitle,
          effectiveDescription: userCard.instanceDescriptionOverride ?? offering.baseDescription,
          uiConfig: mergedUiConfig,
          instance_data: userCardInstanceData,
          instance_status: userCard.instanceStatus,
          sort_order: userCard.userSortOrder, // Use user card's sort order
        };
        finalCardViews.push(cardView);
      } else {
        // Orphaned user card - related offering might be inactive or deleted
        console.warn(`[CARDS_USER] Warning: User card ${userCard.userCardId} references unknown or inactive offering ID ${userCard.coachOfferingId}. Skipping.`);
      }
    }

    // Iterate through ACCESSIBLE offerings to add defaults for those without user cards
    // AND to always add the default GUIDED_SESSION offering if it's accessible
    for (const offering of accessibleOfferings) { // Use accessibleOfferings here
      // *** ADDED CHECK: Only add default view if the offering itself is visible ***
      if (offering.isVisible !== 1) {
        console.log(`[CARDS_DEFAULT_SKIP] Skipping default for Offering ${offering.coachOfferingId} (Type: ${offering.type}) because it is not visible (is_visible=0).`);
        continue; 
      }

      const isGuidedSession = offering.type === 'GUIDED_SESSION';
      const hasUserCard = processedOfferingIds.has(offering.coachOfferingId);
      const shouldAddDefault = !hasUserCard && offering.requiresUserInstance === 0;

      // Add default if it's a GUIDED_SESSION OR if it meets the standard default criteria
      if (isGuidedSession || shouldAddDefault) {
        // Log why we are adding this default
        if (isGuidedSession) {
            console.log(`[CARDS_DEFAULT_ADD] Adding default GUIDED_SESSION offering ${offering.coachOfferingId}. Has user card? ${hasUserCard}`);
        } else {
            console.log(`[CARDS_DEFAULT_ADD] Creating standard default view for Offering ${offering.coachOfferingId} (Type: ${offering.type}).`);
        }

        const defaultUiConfig = typeof offering.defaultUiConfig === 'string' ? JSON.parse(offering.defaultUiConfig) : offering.defaultUiConfig;
        const offeringUiConfigOverride = typeof offering.uiConfigOverride === 'string' ? JSON.parse(offering.uiConfigOverride) : offering.uiConfigOverride;
        const offeringDataParsed = typeof offering.offeringData === 'string' ? JSON.parse(offering.offeringData) : (offering.offeringData || {});
        const mergedUiConfig = mergeUiConfigs(defaultUiConfig, offeringUiConfigOverride);

        const cardView: UserCardView = {
          user_card_id: null, // It's a default view, so no user_card_id
          coach_offering_id: offering.coachOfferingId,
          type: offering.type,
          effectiveTitle: offering.baseTitle,
          effectiveDescription: offering.baseDescription,
          uiConfig: mergedUiConfig,
          instance_data: offeringDataParsed,
          instance_status: 'AVAILABLE',
          sort_order: offering.offeringSortOrder, // Use offering's sort order for defaults
        };
        finalCardViews.push(cardView);

      } else if (!isGuidedSession && hasUserCard) {
          // Log skipping standard defaults that have user cards
          console.log(`[CARDS_DEFAULT_SKIP] Skipping default for Offering ${offering.coachOfferingId} (Type: ${offering.type}) because a user card exists.`);
      } else if (!isGuidedSession && !hasUserCard && offering.requiresUserInstance !== 0) {
          // Log skipping standard defaults that require user instances
          console.log(`[CARDS_DEFAULT_SKIP] Skipping default for Offering ${offering.coachOfferingId} (Type: ${offering.type}) because requiresUserInstance is TRUE.`);
      }
    }

    // 4. Final Sort (Apply sort_order from the merged views)
    finalCardViews.sort((a, b) => a.sort_order - b.sort_order);

    console.log(`[CARDS] Returning ${finalCardViews.length} merged card views`);
    console.log('[CARDS] Effective Titles & Descriptions:');
    finalCardViews.forEach((card, index) => {
      console.log(`  [${index}] ID: ${card.user_card_id ?? card.coach_offering_id} Type: ${card.type} Status: ${card.instance_status} Sort: ${card.sort_order}`);
      console.log(`      Title: ${card.effectiveTitle}`);
      console.log(`      Desc: ${card.effectiveDescription}`);
    });

    return finalCardViews;

  } catch (error) {
    console.error("[CARDS] Error fetching or merging user cards:", error);
    // Depending on requirements, you might want to return an empty array or throw
    return [];
  }
};

/**
 * Fetches detailed information about a coach by name
 * 
 * @param coachName - The name of the coach to fetch details for
 * @returns Promise<{ name: string; description: string | null; metadata: any } | null> - Coach details or null if not found
 */
export const getCoachDetailsAction = async (coachName: string) => {
  try {
    if (!coachName) {
      console.log("[COACH_DETAILS] No coach name provided");
      return null;
    }
    
    // Get the coach by name
    const coach = await db
      .selectFrom("coaches")
      .select(["name", "description", "metadata", "type", "cost_multiplier"])
      .where("name", "=", coachName)
      .executeTakeFirst();

    if (!coach) {
      console.log("[COACH_DETAILS] No coach found with name:", coachName);
      return null;
    }

    // Parse metadata if it's a string
    let parsedMetadata = {};
    if (coach.metadata) {
      try {
        parsedMetadata = typeof coach.metadata === 'string' 
          ? JSON.parse(coach.metadata) 
          : coach.metadata;
      } catch (error) {
        console.error("[COACH_DETAILS] Error parsing coach metadata:", error);
      }
    }

    // Determine cost multiplier, default to 1 if null/undefined or not a number
    const rawMult = coach.costMultiplier ?? (coach as any).cost_multiplier; // fallback if mapping fails
    const costMultiplier = isNaN(Number(rawMult)) ? 1 : Number(rawMult);
    console.log(`[COACH_DETAILS] Raw multiplier value from DB: ${rawMult}, using: ${costMultiplier}`);

    // Calculate cost rates using base action costs and multiplier
    const baseCallCost = getActionCost("call", 1);
    const baseMessageCost = getActionCost("message", 1);
    const essencePerMinute = essenceCost(baseCallCost, costMultiplier);
    const essencePerMessage = essenceCost(baseMessageCost, costMultiplier);
    console.log(`[COACH_DETAILS] Calculated costs — perMin: ${essencePerMinute}, perMsg: ${essencePerMessage}`);

    return {
      name: coach.name,
      description: coach.description,
      type: coach.type,
      metadata: parsedMetadata,
      costMultiplier,
      essencePerMinute,
      essencePerMessage
    };
  } catch (error) {
    console.error("[COACH_DETAILS] Error getting coach details:", error);
    return null;
  }
};

/**
 * Initiates the creation process for a dynamic user card.
 * Creates a placeholder row in user_cards with status 'GENERATING' and calculates
 * the correct negative sort_order to place it at the top.
 *
 * @param channelId - The user's channel ID.
 * @param coachName - The name of the coach.
 * @param targetCardType - The type of card being created (e.g., 'GUIDED_SESSION').
 * @returns Promise<{ success: true, userCardId: string } | { success: false, error: string }>
 */
export const initiateUserCardCreationAction = async (
  channelId: number,
  coachName: string,
  targetCardType: string
): Promise<{ success: true, userCardId: string } | { success: false, error: string }> => {
  console.log(`[CARD_INIT] Initiating card creation for channel ${channelId}, coach ${coachName}, type ${targetCardType}`);
  try {
    // 1. Find the coach offering
    const offering = await db
      .selectFrom("coach_card_offerings")
      .select("id")
      .where("coach_name", "=", coachName)
      .where("type", "=", targetCardType)
      // Optional: Add .where("is_active", "=", true) if applicable
      .executeTakeFirst();

    if (!offering) {
      const errorMsg = `[CARD_INIT] Failed: No active coach offering found for coach '${coachName}' and type '${targetCardType}'.`;
      console.error(errorMsg);
      return { success: false, error: errorMsg };
    }
    const coachOfferingId = offering.id;
    console.log(`[CARD_INIT] Found offering ID: ${coachOfferingId}`);

    // 2. Calculate new sort order (place at the end)
    const maxOrderResult = await db
      .selectFrom("user_cards as uc")
      .innerJoin("coach_card_offerings as cco", "uc.coach_offering_id", "cco.id")
      .select(db.fn.max("uc.sort_order").as("maxSortOrder")) // Use max instead of min
      .where("uc.user_channel_id", "=", channelId)
      .where("cco.coach_name", "=", coachName) // Filter by the same coach
      .executeTakeFirst();

    // Access using the alias maxSortOrder
    const maxExistingOrderRaw = maxOrderResult?.maxSortOrder;
    const maxExistingOrder = typeof maxExistingOrderRaw === 'string' ? parseInt(maxExistingOrderRaw, 10) : maxExistingOrderRaw;

    let newSortOrder: number;
    if (maxExistingOrder === null || maxExistingOrder === undefined || isNaN(maxExistingOrder)) {
      newSortOrder = 0; // Start at 0 if no cards exist
    } else {
      newSortOrder = maxExistingOrder + 1; // Increment the max existing order
    }
    console.log(`[CARD_INIT] Calculated new sort order: ${newSortOrder} (Max existing: ${maxExistingOrder})`);

    // 3. Generate UUID
    const newUserCardId = uuidv4();
    console.log(`[CARD_INIT] Generated new user card ID: ${newUserCardId}`);

    // 4. Insert placeholder row
    const insertResult = await db
      .insertInto("user_cards")
      .values({
        id: newUserCardId,
        user_channel_id: channelId,
        coach_offering_id: coachOfferingId,
        type: targetCardType, // Use the target type
        data: '{}', // Insert an empty JSON STRING instead of an object
        status: 'GENERATING',
        is_visible: 1,
        sort_order: newSortOrder,
        title_override: null, // Ensure these are null initially
        description_override: null,
        // last_used_at will default to null in the DB or can be set explicitly if needed
      })
      .executeTakeFirst(); // Use executeTakeFirst or execute for insert

    // Check if the insert was successful (specific checks depend on the DB driver/Kysely setup)
    // For PostgreSQL with `returning`, numInsertedOrUpdatedRows might be useful.
    // For others, just checking for errors might be sufficient.
    // Assuming success if no error is thrown.

    console.log(`[CARD_INIT] Successfully inserted placeholder user card: ${newUserCardId}`);
    return { success: true, userCardId: newUserCardId };

  } catch (error) {
    console.error("[CARD_INIT] Error initiating user card creation:", error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
};

/**
 * Completes the creation process for a dynamic user card.
 * Updates the status, data, title, and description of a card previously marked as 'GENERATING'.
 *
 * @param userCardId - The UUID of the user card to update.
 * @param instanceData - The specific data for this card instance (e.g., { sessionPlan: "..." }).
 * @param titleOverride - Optional title for the card.
 * @param descriptionOverride - Optional description for the card.
 * @returns Promise<{ success: true } | { success: false, error: string }>
 */
export const completeUserCardCreationAction = async (
  userCardId: string,
  instanceData: Record<string, any>,
  titleOverride?: string | null,
  descriptionOverride?: string | null
): Promise<{ success: true } | { success: false, error: string }> => {
  console.log(`[CARD_COMPLETE] Completing creation for user card ID: ${userCardId}`);
  try {
    const updateResult = await db
      .updateTable("user_cards")
      .set({
        status: 'AVAILABLE',
        // *** Explicitly stringify the JSON data ***
        data: JSON.stringify(instanceData), 
        title_override: titleOverride,
        description_override: descriptionOverride,
        updated_at: sql`CURRENT_TIMESTAMP`,
      })
      .where("id", "=", userCardId)
      .where("status", "=", 'GENERATING') // IMPORTANT: Only update if it's still generating
      .executeTakeFirst(); // Returns metadata about the update

    // Check if any row was actually updated
    // Note: numUpdatedRows might be BigInt, convert to Number for comparison
    const numUpdated = Number(updateResult.numUpdatedRows ?? 0);

    if (numUpdated === 0) {
      const errorMsg = `[CARD_COMPLETE] Failed: No user card found with ID ${userCardId} and status 'GENERATING' to update.`;
      console.warn(errorMsg); // Warn because the generation might have finished/failed elsewhere
      // Decide if this should be a hard failure or handled differently
      return { success: false, error: errorMsg };
    }

    console.log(`[CARD_COMPLETE] Successfully updated user card ${userCardId} to 'AVAILABLE'.`);
    return { success: true };

  } catch (error) {
    console.error(`[CARD_COMPLETE] Error completing user card creation for ${userCardId}:`, error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
};

/**
 * Retrieves the list of available coaches for a user based on their coach_access field
 * (and optionally their subscription).
 *
 * @param channelId - The user's channel ID.
 * @returns An array of coach names. If none are found, returns ["Kokoro"] as a fallback.
 */
export const getUserCoachesAction = async (channelId: string | number): Promise<string[]> => {
  if(!(await verifyUserPermission(channelId.toString()))) return ["Kokoro"];

  try {
    // Fetch the user's coach_access and subscription info.
    const user = await db
      .selectFrom("user")
      .select(["coachAccess", "stripeSubscriptionId"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      console.log(`[GET_USER_COACHES] No user found for channel ${channelId}`);
      return ["Kokoro"];
    }

    // Parse the coach_access field.
    const userCoachAccess = typeof user.coachAccess === "string"
      ? JSON.parse(user.coachAccess)
      : user.coachAccess || {};

    // Create an array of coach names for which the value is truthy.
    let availableCoaches = Object.keys(userCoachAccess).filter((coachName) => userCoachAccess[coachName]);

    // Additionally, if the user has a subscription, merge in any additional access.
    if (user.stripeSubscriptionId) {
      const subscription = await db
        .selectFrom("subscription")
        .select(["coach_access"])
        .where("id", "=", user.stripeSubscriptionId)
        .where("status", "=", "active")
        .executeTakeFirst();
      if (subscription) {
        const subscriptionCoachAccess = typeof subscription.coach_access === "string"
          ? JSON.parse(subscription.coach_access)
          : subscription.coach_access || {};
        const subscriptionCoaches = Object.keys(subscriptionCoachAccess).filter((coachName) => subscriptionCoachAccess[coachName]);
        // Merge the two lists, making sure to remove duplicates.
        availableCoaches = Array.from(new Set([...availableCoaches, ...subscriptionCoaches]));
      }
    }

    // If the user has no access entries, default to Kokoro.
    if (availableCoaches.length === 0) {
      availableCoaches = ["Kokoro"];
    }

    console.log(`[GET_USER_COACHES] Available coaches for channel ${channelId}:`, availableCoaches);
    return availableCoaches;
  } catch (error) {
    console.error("[GET_USER_COACHES] Error retrieving user coaches:", error);
    return ["Kokoro"];
  }
};

/**
 * Converts a URL slug to a coach name by fetching all coaches and finding a match
 * 
 * @param slug - The URL slug (e.g., "jp-morgan")
 * @returns Promise<string | null> - The coach name or null if not found
 */
export const getCoachNameFromSlugAction = async (slug: string): Promise<string | null> => {
  try {
    console.log("[SLUG_TO_NAME] Converting slug to coach name:", slug);
    
    // Special case mappings
    const specialCaseMappings: Record<string, string> = {
      'jpmorganjr': 'JP AI'
    };
    
    if (specialCaseMappings[slug.toLowerCase()]) {
      const mappedName = specialCaseMappings[slug.toLowerCase()];
      console.log("[SLUG_TO_NAME] Found special case mapping:", slug, "->", mappedName);
      return mappedName;
    }
    
    // Get all coaches from the database
    const coaches = await db
      .selectFrom("coaches")
      .select(["name"])
      .execute();

    // Convert slug back to potential coach name formats
    const slugLower = slug.toLowerCase();
    
    // Find matching coach by converting names to slug format and comparing
    for (const coach of coaches) {
      const coachSlug = coach.name
        .toLowerCase()
        .replace(/\s+/g, '-')  // Replace spaces with hyphens
        .replace(/[^a-z0-9-]/g, '');  // Remove special characters except hyphens
      
      if (coachSlug === slugLower) {
        console.log("[SLUG_TO_NAME] Found matching coach:", coach.name);
        return coach.name;
      }
    }

    console.log("[SLUG_TO_NAME] No matching coach found for slug:", slug);
    return null;
  } catch (error) {
    console.error("[SLUG_TO_NAME] Error converting slug to coach name:", error);
    return null;
  }
};

/**
 * Connects a user to a new coach by updating their coach_access field
 * and sets the coach as their selected coach
 * 
 * @param channelId - The user's channel ID
 * @param coachName - The coach name to connect to
 * @returns Promise<{ success: boolean; error?: string }>
 */
export const connectUserToCoachAction = async (channelId: string, coachName: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("[CONNECT_COACH] Connecting user to coach:", coachName, "for user:", channelId);
    
    // Verify user permission
    if (!(await verifyUserPermission(channelId))) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if coach exists
    const coach = await db
      .selectFrom("coaches")
      .select(["name"])
      .where("name", "=", coachName)
      .executeTakeFirst();

    if (!coach) {
      return { success: false, error: "Coach not found" };
    }

    // Get user's current coach access
    const user = await db
      .selectFrom("user")
      .select(["coachAccess"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      return { success: false, error: "User not found" };
    }

    // Parse existing coach access
    const currentCoachAccess = typeof user.coachAccess === 'string' 
      ? JSON.parse(user.coachAccess)
      : (user.coachAccess as Record<string, boolean> || {});

    // Check if user already has access to this coach
    if (currentCoachAccess[coachName]) {
      // User already has access, just set this coach as selected
      const selectedCoachUpdateSuccess = await updateSelectedCoach(Number(channelId), coachName);
      if (!selectedCoachUpdateSuccess) {
        console.warn("[CONNECT_COACH] Failed to update selected coach for existing access");
      }
      console.log("[CONNECT_COACH] User already has access to coach, set as selected:", coachName);
      return { success: true };
    }

    // Add the new coach to the access list
    const updatedCoachAccess = {
      ...currentCoachAccess,
      [coachName]: true
    };

    // Update the user's coach access in the database
    await db
      .updateTable("user")
      .set({
        coachAccess: JSON.stringify(updatedCoachAccess),
        selectedCoach: coachName
      })
      .where("channelId", "=", Number(channelId))
      .execute();

    // Initialize user_coach_attributes for this new coach connection
    try {
      // Check if attributes already exist (in case of race conditions)
      const existingAttrs = await db
        .selectFrom("user_coach_attributes")
        .select(["channel_id"])
        .where("channel_id", "=", Number(channelId))
        .where("coach_name", "=", coachName)
        .executeTakeFirst();

      if (!existingAttrs) {
        await db
          .insertInto("user_coach_attributes")
          .values({
            channel_id: Number(channelId),
            coach_name: coachName,
            data_sharing_enabled: 0, // SQLite boolean as integer
            coach_notes: "",
            meta_feedback: "", // Start with empty metafeedback
            messages_till_feedback: 4
          })
          .execute();
        console.log("[CONNECT_COACH] Initialized coach attributes for:", coachName);
      }
    } catch (attrError) {
      console.error("[CONNECT_COACH] Error initializing coach attributes:", attrError);
      // Don't fail the connection if attributes initialization fails
    }

    // Also set this coach as the user's selected coach
    const selectedCoachUpdateSuccess = await updateSelectedCoach(Number(channelId), coachName);
    if (!selectedCoachUpdateSuccess) {
      console.warn("[CONNECT_COACH] Failed to update selected coach, but connection was successful");
    }

    console.log("[CONNECT_COACH] Successfully connected user to coach:", coachName);
    return { success: true };
  } catch (error) {
    console.error("[CONNECT_COACH] Error connecting user to coach:", error);
    return { success: false, error: "Database error" };
  }
};

/**
 * Grants coach access without changing the user's selected coach.
 * Useful for adding default access (e.g., JP AI) on normal sign-ups.
 */
export const grantCoachAccessAction = async (
  channelId: string,
  coachName: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    if (!(await verifyUserPermission(channelId))) {
      return { success: false, error: "Unauthorized" };
    }

    // Ensure coach exists
    const coach = await db
      .selectFrom("coaches")
      .select(["name"]) 
      .where("name", "=", coachName)
      .executeTakeFirst();

    if (!coach) {
      return { success: false, error: "Coach not found" };
    }

    // Read current access
    const user = await db
      .selectFrom("user")
      .select(["coachAccess"]) 
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      return { success: false, error: "User not found" };
    }

    const currentCoachAccess = typeof user.coachAccess === 'string'
      ? JSON.parse(user.coachAccess)
      : (user.coachAccess as Record<string, boolean> || {});

    if (currentCoachAccess[coachName]) {
      return { success: true };
    }

    const updatedCoachAccess = { ...currentCoachAccess, [coachName]: true };

    await db
      .updateTable("user")
      .set({ coachAccess: JSON.stringify(updatedCoachAccess) })
      .where("channelId", "=", Number(channelId))
      .execute();

    // Optionally initialize attributes (safe no-op if exists)
    try {
      const existingAttrs = await db
        .selectFrom("user_coach_attributes")
        .select(["channel_id"]) 
        .where("channel_id", "=", Number(channelId))
        .where("coach_name", "=", coachName)
        .executeTakeFirst();

      if (!existingAttrs) {
        await db
          .insertInto("user_coach_attributes")
          .values({
            channel_id: Number(channelId),
            coach_name: coachName,
            data_sharing_enabled: 0,
            coach_notes: "",
            meta_feedback: "",
            messages_till_feedback: 4,
          })
          .execute();
      }
    } catch {}

    return { success: true };
  } catch (error) {
    console.error("[GRANT_COACH_ACCESS] Error:", error);
    return { success: false, error: "Database error" };
  }
};

/**
 * Gets all available coaches from the database
 * 
 * @returns Promise<string[]> - Array of coach names
 */
export const getAllCoachesAction = async (): Promise<string[]> => {
  try {
    console.log("[GET_ALL_COACHES] Fetching all coaches from database");
    
    const coaches = await db
      .selectFrom("coaches")
      .select(["name"])
      .execute();

    const coachNames = coaches.map(coach => coach.name);
    console.log("[GET_ALL_COACHES] Found coaches:", coachNames);
    
    return coachNames;
  } catch (error) {
    console.error("[GET_ALL_COACHES] Error fetching coaches:", error);
    return [];
  }
};

/**
 * Gets whether data sharing is enabled for a specific user–coach pair.
 * Returns false if no record exists.
 */
export const getDataSharingEnabledAction = async (
  channelId: number,
  coachName: string,
): Promise<boolean> => {
  if (!(await verifyUserPermission(String(channelId)))) return false;
  try {
    const record = await db
      .selectFrom("userCoachAttributes")
      .select(["dataSharingEnabled"])
      .where("channelId", "=", channelId)
      .where("coachName", "=", coachName)
      .executeTakeFirst();

    return record ? record.dataSharingEnabled === 1 : false;
  } catch (error) {
    console.error("[DATA_SHARING] Error fetching preference:", error);
    return false;
  }
};

/**
 * Sets the dataSharingEnabled flag for a user–coach pair. Creates the row if needed.
 */
export const setDataSharingEnabledAction = async (
  channelId: number,
  coachName: string,
  enabled: boolean,
): Promise<boolean> => {
  if (!(await verifyUserPermission(String(channelId)))) return false;
  try {
    const existing = await db
      .selectFrom("userCoachAttributes")
      .select(["channelId"])
      .where("channelId", "=", channelId)
      .where("coachName", "=", coachName)
      .executeTakeFirst();

    if (existing) {
      await db
        .updateTable("userCoachAttributes")
        .set({ dataSharingEnabled: enabled ? 1 : 0 })
        .where("channelId", "=", channelId)
        .where("coachName", "=", coachName)
        .execute();
    } else {
      await db
        .insertInto("userCoachAttributes")
        .values({
          channelId,
          coachName,
          dataSharingEnabled: enabled ? 1 : 0,
          coachNotes: "",
        })
        .execute();
    }
    return true;
  } catch (error) {
    console.error("[DATA_SHARING] Error saving preference:", error);
    return false;
  }
};
