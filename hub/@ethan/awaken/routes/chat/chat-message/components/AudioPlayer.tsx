"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import { But<PERSON> } from "@reframe/ui/main.tsx";
import { PlayIcon } from "@reframe/icons/play.ts";
import { PauseIcon } from "@reframe/icons/pause-icon.ts";

export interface AudioPlayerProps {
  audio: string;
  index: number;
  activeAudioId: number | null;
  setActiveAudioId: (id: number | null) => void;
  onDuration?: (seconds: number) => void;
}

// Re-export the shared hook so that parent modules can keep importing
// it from this file without change. The actual implementation now
// lives in `routes/chat/hooks/useAudioPlayer.tsx`.
export { useAudioPlayer } from "../../../../lib/hooks/useAudioPlayer.tsx";

/**
 * AudioPlayer component with lazy initialization and proper cleanup
 */
export const AudioPlayer = ({ 
  audio, 
  index, 
  activeAudioId, 
  setActiveAudioId,
  onDuration,
}: AudioPlayerProps) => {
  // Track local audio progress
  const [progress, setProgress] = useState<number>(0);
  // We'll only track whether this specific player SHOULD be playing
  const isActive = activeAudioId === index;
  // Reference to functions for cleanup
  const cleanupFnsRef = useRef<{updateFn?: () => void, endFn?: () => void}>({});
  // Reference to the audio element created on demand
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  // Track if audio is actually playing (not just active)  
  const [isPlaying, setIsPlaying] = useState(false);
  // Reference to track any created object URL (for cleanup)
  const objectUrlRef = useRef<string | null>(null);

  // Initialize audio element and listeners once per audio URL
  useEffect(() => {
    if (!audio || (typeof audio === 'string' && audio.length === 0)) return;

    let el = audioElementRef.current;
    if (!el) {
      el = new Audio();
      el.autoplay = false;
      audioElementRef.current = el;
    }

    // Always ensure src is up to date
    if (el.src !== audio) {
      try { el.src = audio; } catch (e) { console.error('Failed to set audio src', e); }
    }

    const updateProgress = () => {
      if (!el) return;
      const { currentTime, duration } = el;
      if (duration > 0) setProgress((currentTime / duration) * 100);
    };

    const handleEnded = () => {
      setProgress(0);
      setIsPlaying(false);
      setActiveAudioId(null);
    };

    const handleLoadedMetadata = () => {
      try {
        if (typeof onDuration === 'function' && isFinite(el!.duration)) {
          onDuration(el!.duration);
        }
      } catch {}
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    // Attach listeners
    el.addEventListener('timeupdate', updateProgress);
    el.addEventListener('ended', handleEnded);
    el.addEventListener('loadedmetadata', handleLoadedMetadata);
    el.addEventListener('play', handlePlay);
    el.addEventListener('pause', handlePause);

    // If metadata already available, surface duration
    if (typeof onDuration === 'function' && el.readyState >= 1 && isFinite(el.duration)) {
      onDuration(el.duration);
    }

    return () => {
      // Detach on unmount or when audio URL changes
      el.removeEventListener('timeupdate', updateProgress);
      el.removeEventListener('ended', handleEnded);
      el.removeEventListener('loadedmetadata', handleLoadedMetadata);
      el.removeEventListener('play', handlePlay);
      el.removeEventListener('pause', handlePause);
    };
  }, [audio, onDuration, setActiveAudioId]);

  // Pause when deactivated; do not tear down listeners
  useEffect(() => {
    const el = audioElementRef.current;
    if (!el) return;
    if (!isActive) {
      try { if (!el.paused) el.pause(); } catch {}
    }
  }, [isActive]);
  
  // Manual play/pause handler - only attempt to play when user clicks
  const handlePlayPause = () => {
    console.log("[AudioPlayer] handlePlayPause called", {
      audio,
      index,
      isActive,
      activeAudioId,
      audioElementRef: audioElementRef.current,
    });
    if (isActive && audioElementRef.current) {
      // This player is already active
      try {
        const audioElem = audioElementRef.current;
        
        if (audioElem.paused) {
          // User wants to play - use an async function to handle the promise
          (async () => {
            try {
              await audioElem.play();
              setIsPlaying(true);
            } catch (error) {
              console.error('Play error:', error);
              setIsPlaying(false);
            }
          })();
        } else {
          // User wants to pause
          audioElem.pause();
          setIsPlaying(false);
          setActiveAudioId(null);
        }
      } catch (error) {
        console.error('Toggle playback error:', error);
        setIsPlaying(false);
      }
    } else {
      // This audio isn't active yet - make it active but don't autoplay
      setProgress(0);
      setActiveAudioId(index);
      // Let the useEffect handle creating the audio - we'll play manually
      setTimeout(() => {
        if (audioElementRef.current) {
          (async () => {
            try {
              await audioElementRef.current.play();
              setIsPlaying(true);
            } catch (error) {
              console.error('Delayed play error:', error);
              setIsPlaying(false);
            }
          })();
        }
      }, 50); // Small delay to ensure audio element is created
    }
  };

  // Only render the player if audio URL exists
  if (!audio) return null;
  
  return (
    <div className="flex items-center gap-2 flex-1">
      <Button
        variant="outline"
        css="w-9 h-9 p-2 rounded-full border border-[#505050] bg-[#272727] hover:bg-[#333333] hover:border-[#FCA311] flex items-center justify-center shrink-0"
        onClick={handlePlayPause}
      >
        {isPlaying ? (
          <PauseIcon className="w-4 h-4 text-[#FCA311]" />
        ) : (
          <PlayIcon className="w-4 h-4 text-[#FCA311]" />
        )}
      </Button>
      <div className="h-1.5 flex-1 min-w-[140px] bg-[#505050] rounded-full overflow-hidden"> 
        <div
          className="h-full bg-[#FCA311] transition-[width] duration-150 ease-out"
          style={{ width: isActive ? `${progress}%` : '0%' }}
        />
      </div>
    </div>
  );
};
