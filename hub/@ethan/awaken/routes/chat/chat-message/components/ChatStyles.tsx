export const loadingOverlayStyles = (contentReady: boolean) => `   
    .loading-overlay {
      position: absolute;
      top: 0;
      width: 100%;
      text-align: center;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 10;
      padding: 8px 0;
      border-radius: 8px 8px 0 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      animation: ${contentReady ? 'fadeIn 0.3s ease' : 'none'};
    } 
    .message-separator {
      height: 1px;
      background: linear-gradient(to right, transparent, rgba(252, 163, 17, 0.3), transparent);
      margin: 8px 0;
      width: 100%;
    }
    
    // Add a new class for the thinking dots animation
    .thinking-dots-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 20px;
      background-color: rgba(149, 149, 149, 0.15);
      border-radius: 24px;
      backdrop-filter: blur(4px);
      width: 100%;
      min-height: 48px;
    }
    
    .thinking-dot {
      width: 4px; /* reduced 50% */
      height: 4px; /* reduced 50% */
      margin: 0 4px;
      border-radius: 50%;
      background-color: #FCA311;
      display: inline-block;
    }
    
    .thinking-dot-1 {
      animation: pulse 1.4s infinite ease-in-out;
    }
    
    .thinking-dot-2 {
      animation: pulse 1.4s infinite ease-in-out;
      animation-delay: 0.2s;
    }
    
    .thinking-dot-3 {
      animation: pulse 1.4s infinite ease-in-out;
      animation-delay: 0.4s;
    }
    
    @keyframes pulse {
      0%, 100% { 
        transform: scale(0.8);
        opacity: 0.5;
      }
      50% { 
        transform: scale(1.2);
        opacity: 1;
      }
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .fade-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, transparent 100%);
      pointer-events: none;
      z-index: 5;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .fade-visible {
      opacity: 1;
    }
    
    /* Message bubble width styles */
    .message-bubble {
      max-width: 85%; /* Default for larger screens */
      min-width: 5%; /* Minimum width for short messages */
    }
    
    @media (max-width: 640px) {
      .message-bubble {
        max-width: 90%; /* Wider on mobile */
        min-width: 15%; /* Maintain minimum width on mobile */
      }
    }
  `;


export const keyboardStyles = `
    /* Fix for iOS keyboard behavior */
    @supports (-webkit-touch-callout: none) {
        .ios-viewport-fix {
        padding-bottom: env(safe-area-inset-bottom);
        min-height: -webkit-fill-available;
        }
    }

    /* Prevent content from being pushed up when keyboard shows */
    @media screen and (max-width: 768px) {
        .input-section {
        position: sticky;
        bottom: 0;
        background: transparent;
        padding-bottom: env(safe-area-inset-bottom, 20px);
        margin-bottom: 0;
        }
    }

    /* Ensure content stays within bounds */
    .content-container {
        min-height: 0;
        height: calc(100dvh - env(safe-area-inset-bottom, 0px));
        display: flex;
        flex-direction: column;
    }
`;