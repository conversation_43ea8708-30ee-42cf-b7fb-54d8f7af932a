"use client";

import React, { useEffect, useRef, useState, useCallback, useMemo } from "npm:react@canary";
import { But<PERSON>, ScrollArea, Text } from "@reframe/ui/main.tsx";

import { BackIcon, ThreadIcon } from "../../../lib/icons.tsx";
import { getDataSharingEnabledAction, getCoachDetailsAction } from "../../../actions/db/coach-actions.ts";

import { Logo } from "../../../lib/logo.tsx";
import { CoachSelector } from "../components/CoachSelector.tsx";
import { CoachOverlay } from "../components/CoachOverlay.tsx";
import { VoiceCall } from "../components/VoiceCall.tsx";
import { UserCardView, getUserCardsForCoachAction } from "../../../actions/db/coach-actions.ts";
import { markCoachThreadMessagesSeenByUser } from "../../../actions/db/conversation-actions.ts";


import { MessageBubble, AwakeningBubble } from "./components/MessageBubble.tsx";
import { ChatInput } from "./components/ChatInput.tsx";
import { useAudioPlayer } from "./components/AudioPlayer.tsx";
import { loadingOverlayStyles, keyboardStyles } from "./components/ChatStyles.tsx";
import { MessageItem } from "./components/MessageItem.tsx";
import { CoachThread } from "./components/CoachThread.tsx";
import { Options } from "./components/Options.tsx";

type ScrollEvent = React.WheelEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>;

export const ChatMessage = ({
  input,
  setInput,
  sendMessage,
  allMessages,
  loading,
  setLoading,
  channelId,
  reset,
  currentLetterIndex,
  messageRendering,
  stopAudio,
  onboarded,
  setOnboarded,
  context,
  toggleStar,
  onBack,
  fullscreenMode = false,
  loadMoreMessages,
  loadingMore = false,
  hasMore = false,
  setHasMore,
  messageCount = 0,
  dailyMessageLimit = 0,
  isFreePlan = false,
  isMobile,
  coachName = "Kokoro",
  isMessageButtonGlowing,
  setIsMessageButtonGlowing,
  availableCoaches = [],
  onCoachChange,
  user,
  // New props for interactive components (card data now handled internally)
  audio,
  rootAudioRef,
  setIsPlaying,
  resetOutputSentence,
  setOutput,
  originalOutput,
  setIsComplete,
  audioScale,
  goalInput,
  onPaymentOpen,
  onTopUpOpen,
  setAllMessages,
  isVoiceCall,
  toggleVoiceCall,
}) => {
  const [contentReady, setContentReady] = useState(true);
  const [scrollAreaVisible, setScrollAreaVisible] = useState(false);
  // Track whether user has enabled data sharing with this coach
  const [dataSharingEnabled, setDataSharingEnabled] = useState(false);
  // Coach type for empty-state placeholder
  const [coachType, setCoachType] = useState<string | null>(null);
  
  const hasUnseenCoachMessages = useMemo(() => {
    return allMessages.some(message => 
      message.Type === 'coach_message' &&
      message.CoachName === coachName &&
      message.Sender !== 'user' &&
      (message.SeenByUser === 0 || message.SeenByUser === null || message.SeenByUser === undefined)
    );
  }, [allMessages, coachName]);

  useEffect(() => {
    if (isMessageButtonGlowing) {
      setIsMessageButtonGlowing(false);
    }
    // Only run on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // Small delay to ensure smooth transition
    requestAnimationFrame(() => {
      setContentReady(true);
      // Initially hide scroll area until positioned
      setScrollAreaVisible(false);
    });
  }, [coachName]);

  const filteredMessages = useMemo(() => {
    return allMessages.filter(message => 
      message.CoachName === coachName
    );
  }, [allMessages, coachName]);


  // console.log("Coach name changed to: ", coachName);
  // console.log("message: ", allMessages);
  // console.log("filteredMessages: ", filteredMessages);

  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Add new state for dynamic loading
  const [anchorMessageId, setAnchorMessageId] = useState<string | null>(null);
  const [preserveScrollPosition, setPreserveScrollPosition] = useState(false);
  const [scrollToIndex, setScrollToIndex] = useState<number | null>(null);

  // Add state to track audio preference

  const [micPermission, setMicPermission] = useState<
    "granted" | "denied" | "prompt" | null
  >(null);

  const lastMessageIdRef = useRef<string | null>(null);
  
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Reference for debounce timer
  const debounceTimerRef = useRef<number | null>(null);

  // Input focus state is now handled by ChatInput component
const [isTextInputFocused] = useState(false);

  // Use the audio player hook for audio state management
const { activeAudioId, setActiveAudioId } = useAudioPlayer();

  const audioChunks = useRef([]);

  const [showContext, setShowContext] = useState("");

  const isCancelingRef = useRef(false);

  // Add new state for fade effects
  const [showFadeBottom, setShowFadeBottom] = useState(false);
  const [isAtBottom, setIsAtBottom] = useState(true);
  // Add a flag to track initial scroll setup
  const hasInitialScrollRef = useRef(false);
  
  // Auto-scroll state management
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(false);
  const lastScrollUpTimeRef = useRef<number | null>(null);
  const currentLetterIndexRef = useRef(currentLetterIndex);
  
  // Reference to track expanded state changes
  const prevExpandedRef = useRef(false);

  const lastTouchY = useRef<number | null>(null);

  const handleScrollIntent = useCallback((e: ScrollEvent) => {
    if (e.type === "wheel") {
      const we = e as React.WheelEvent<HTMLDivElement>;
      if (we.deltaY < 0) {
        // console.log("SCROLL UP INTENDED");
        // Disable auto-scroll when user scrolls up
        if (autoScrollEnabled) {
          lastScrollUpTimeRef.current = Date.now();
          setAutoScrollEnabled(false);
        }
      }
    }
    // finger start: just record initial Y
    else if (e.type === "touchstart") {
      lastTouchY.current = (e as React.TouchEvent<HTMLDivElement>).touches[0].clientY;
    }
    // finger moving: compare to last Y
    else if (e.type === "touchmove") {
      const te = e as React.TouchEvent<HTMLDivElement>;
      const currY = te.touches[0].clientY;
      if (lastTouchY.current != null && currY > lastTouchY.current) {
        // console.log("SCROLL UP INTENDED");
        // Disable auto-scroll when user scrolls up
        if (autoScrollEnabled) {
          lastScrollUpTimeRef.current = Date.now();
          setAutoScrollEnabled(false);
        }
      }
      lastTouchY.current = currY;
    }
  }, [autoScrollEnabled]);

  // Handler for dynamic loading when scrolling to top
  const handleScrollForLoading = useCallback(() => {
    if (!scrollAreaRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
    const scrollBuffer = 150; // Buffer to determine "close to top"
    const bottomBuffer = 100; // Increased buffer for better detection of bottom
    
    // Check if we're near the top for loading more messages
    if (scrollTop < scrollBuffer && hasMore && !loadingMore) {
      // Before fetching older messages, remember which message is at/near the top
      if (allMessages.length > 0) {
        // Determine which message element is currently at the very top of the
        // viewport so that we can keep it fixed after we prepend older
        // messages.  Relying on the first item in the data array is wrong
        // once the user has scrolled a bit.

        if (scrollAreaRef.current) {
          const scrollArea = scrollAreaRef.current;

          // Grab all rendered message elements (they carry data-id attrs).
          const msgEls = Array.from(
            scrollArea.querySelectorAll('[data-id]')
          ) as HTMLElement[];

          // Find the first element whose top edge is at or below the scroll
          // container’s own top edge.  That is the element the user is
          // currently seeing at the very top of the list.
          const firstVisibleEl = msgEls.find((el) => {
            const elTop = el.getBoundingClientRect().top;
            const containerTop = scrollArea.getBoundingClientRect().top;
            return elTop >= containerTop;
          });

          const anchorId = firstVisibleEl?.dataset.id;

          if (anchorId) {
            setAnchorMessageId(anchorId);
          }
        }
        // Also store its index for later positioning
        setScrollToIndex(0);
      }

      // Trigger loading of older messages
      setPreserveScrollPosition(true);
      loadMoreMessages();
    }
    
    // Update fade effects
    
    // Only show bottom fade and scroll button if not at bottom
    const isScrolledToBottom = scrollHeight - scrollTop - clientHeight < bottomBuffer;
    setIsAtBottom(isScrolledToBottom);
    setShowFadeBottom(!isScrolledToBottom && scrollHeight > clientHeight);
  }, [hasMore, loadingMore, loadMoreMessages, allMessages, filteredMessages, coachName]);

  // Update UI visibility immediately during scroll
  const updateScrollVisibility = useCallback(() => {
    if (!scrollAreaRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current;
    const bottomBuffer = 100; // Increased buffer for better detection of bottom
    
    // Update fade effects immediately
    
    // Immediately update bottom arrow visibility
    const isScrolledToBottom = scrollHeight - scrollTop - clientHeight < bottomBuffer;
    setIsAtBottom(isScrolledToBottom);
    setShowFadeBottom(!isScrolledToBottom && scrollHeight > clientHeight);
    
    // Re-enable auto-scroll if user scrolls to bottom while message is still rendering
    if (isScrolledToBottom && !autoScrollEnabled && messageRendering && 
      lastScrollUpTimeRef.current && Date.now() - lastScrollUpTimeRef.current > 1000
    ) {
      setAutoScrollEnabled(true);
    }
  }, [autoScrollEnabled, messageRendering]);

  // Scroll to bottom function - reusable
  const scrollToBottom = useCallback((behavior: ScrollBehavior = 'instant') => {
    if (!scrollAreaRef.current) return;
    
    // Add a small delay only for smooth scrolling, not for initial 'auto' scroll
    const scrollAction = () => {
      scrollAreaRef?.current?.scrollTo({
        top: scrollAreaRef?.current?.scrollHeight + 40, // Add extra padding to ensure visibility
        behavior
      });
    };

    if (behavior === 'auto') {
      scrollAction(); // Execute immediately for auto scroll
    } else {
      setTimeout(scrollAction, 10); // Delay for smooth scroll
    }
  }, []);

  // Initial scroll setup using layout effect to ensure it happens before painting

  // Add effect to track regular scroll behavior
  useEffect(() => {
    const handleScroll = () => {
      // Skip scroll events during initial setup
      if (!hasInitialScrollRef.current) return;
      
      // Update UI immediately during scroll
      updateScrollVisibility();
      
      // Debounce the expensive operations
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      
      debounceTimerRef.current = setTimeout(() => {
        handleScrollForLoading();
        debounceTimerRef.current = null;
      }, 100) as unknown as number;
    };

    const scrollArea = scrollAreaRef.current;
    if (scrollArea) {
      scrollArea.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollArea) {
        scrollArea.removeEventListener("scroll", handleScroll);
      }
      // Clean up any pending debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [handleScrollForLoading, updateScrollVisibility]);

  useEffect(() => {
    if (!hasInitialScrollRef.current) return;
    
    if (scrollAreaRef.current && allMessages.length > 0 && isAtBottom) {
      scrollToBottom();
    }
  }, [allMessages, isAtBottom, scrollToBottom]);

  // Improved effect to scroll to bottom when fullscreen mode is enabled
  useEffect(() => {
    if (fullscreenMode && hasInitialScrollRef.current) {
      scrollToBottom();
    }
  }, [fullscreenMode, scrollToBottom]);

  // Scroll to bottom when coachName changes - always scroll to bottom when coach changes
  useEffect(() => {
    if (contentReady) {
      // Hide scroll area first
      setScrollAreaVisible(false);
      
      // Position at bottom immediately, then show
      setTimeout(() => {
        if (scrollAreaRef.current) {
          // Set scroll position instantly without any animation
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
          // Ensure hasInitialScrollRef is set
          hasInitialScrollRef.current = true;
          // Now show the scroll area
          setScrollAreaVisible(true);
        }
      }, 0);
      
      // iOS Safari fix: Force scroll area to refresh when coach changes
      if (scrollAreaRef.current && /iPad|iPhone|iPod/.test(navigator.userAgent)) {
        setTimeout(() => {
          if (scrollAreaRef.current) {
            const scrollArea = scrollAreaRef.current;
            
            // Force layout recalculation
            scrollArea.style.display = 'none';
            scrollArea.offsetHeight; // Trigger reflow
            scrollArea.style.display = '';
            
            // Set scroll position directly
            scrollArea.scrollTop = scrollArea.scrollHeight;
          }
        }, 50);
      }
    }
  }, [coachName, contentReady]);

  useEffect(() => {
    setHasMore(true);
  }, [coachName])

  // Use MutationObserver to preserve scroll position during loading
  useEffect(() => {
    let observer: MutationObserver | null = null;

    if (preserveScrollPosition && scrollAreaRef.current) {
      // Get all message elements initially
      const scrollArea = scrollAreaRef.current;
      const initialElements = scrollArea.querySelectorAll('[data-id]');
      const anchorElement = initialElements[0]; // Current first element
      let initialTop = 0;
      
      if (anchorElement) {
        // Get the anchor element's position relative to viewport
        const rect = anchorElement.getBoundingClientRect();
        initialTop = rect.top;
      }

      observer = new MutationObserver(() => {
        // After DOM changes, find the anchor element again
        const newAnchorElement = scrollArea.querySelector(`[data-id="${anchorMessageId}"]`);
        
        if (newAnchorElement) {
          // Calculate how much to adjust scroll to keep anchor at same position
          const newRect = newAnchorElement.getBoundingClientRect();
          const diff = newRect.top - initialTop;
          
          if (Math.abs(diff) > 2) { // Only adjust if there's a significant difference
            scrollArea.scrollTop += diff;
          }
        }
      });

      // Observe changes to the scroll area's children
      observer.observe(scrollArea, { childList: true, subtree: true });
    }

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [preserveScrollPosition, anchorMessageId]);

  // Scroll to show 2 messages above the anchor after loading completes
  useEffect(() => {
    // Once loading is finished...
    if (!loadingMore && anchorMessageId && scrollToIndex !== null && scrollAreaRef.current) {
      const scrollArea = scrollAreaRef.current;
      
      // Stop preserving scroll position so we can scroll smoothly now
      setPreserveScrollPosition(false);
      
      // Give React/DOM a moment to render messages
      setTimeout(() => {
        // Get all message elements
        const messageElements = Array.from(scrollArea.querySelectorAll('[data-id]'));
        
        // Find the anchor index in the new list
        const anchorElementIndex = messageElements.findIndex(
          el => (el as HTMLElement).dataset.id === anchorMessageId
        );
        
        if (anchorElementIndex !== -1) {
          // Calculate target index: 1 message NEWER than the anchor (not older)
          // When prepending items, anchor moves down in the list, so we subtract to find newer messages
          // Ensure we don't get negative index
          const targetIndex = Math.max(0, anchorElementIndex - 1);
          
          console.log(`Scrolling from anchor ${anchorElementIndex} to target ${targetIndex}`);
          
          // Get the target element
          const targetElement = messageElements[targetIndex];
          
          if (targetElement) {
            // Scroll smoothly to the target message's position
            scrollArea.scrollTo({
              top: (targetElement as HTMLElement).offsetTop - 20, // Add small offset for better visibility
              behavior: "smooth",
            });
          }
        }
        
        // Reset state
        setAnchorMessageId(null);
        setScrollToIndex(null);
      }, 100);
    }
  }, [loadingMore, anchorMessageId, scrollToIndex]);

  // Prevent page scrolling but allow normal scrolling in the content area
  useEffect(() => {
    // Only prevent document body scrolling
    document.body.style.overflow = 'hidden';
    
    // Allow the component to scroll normally
    
    return () => {
      // Clean up when component unmounts
      document.body.style.overflow = '';
    };
  }, []);

  useEffect(() => {
    // Check microphone permission status
    navigator.permissions
      .query({ name: "microphone" as PermissionName })
      .then((status) => {
        setMicPermission(status.state);

        // Listen for changes in permission status
        status.onchange = () => {
          setMicPermission(status.state);
        };
      });
  }, []);

  // Input focus is now handled by the ChatInput component

  useEffect(() => {
    if (!scrollAreaRef.current || !allMessages?.length) return;
    const newLastId = allMessages[allMessages.length - 1].Id;
    
    if (newLastId !== lastMessageIdRef.current) {
      // If a new message arrived (not just a star toggle), scroll.
      scrollAreaRef.current.scrollTo({ top: scrollAreaRef.current.scrollHeight, behavior: "smooth" });
      lastMessageIdRef.current = newLastId;
    }
  }, [allMessages]);

  // Enable auto-scroll when new message starts rendering (currentLetterIndex goes from 0 to 1)
  // Disable auto-scroll when message finishes rendering (currentLetterIndex becomes 0)
  useEffect(() => {
    if (messageRendering) {
      setAutoScrollEnabled(true);
    } else {
      setAutoScrollEnabled(false);
    }
  }, [messageRendering]);

  // Auto-scroll to bottom when currentLetterIndex changes (only if auto-scroll is enabled)
  useEffect(() => {
    if (scrollAreaRef.current && autoScrollEnabled && messageRendering) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight + 40, // Add extra padding to ensure visibility
        behavior: "smooth"
      });
    }
  }, [currentLetterIndex, autoScrollEnabled, messageRendering]);

  // On initial load, scroll instantly to bottom without animation
  useEffect(() => {
    if (contentReady && scrollAreaRef.current && !hasInitialScrollRef.current) {
      // Hide scroll area first
      setScrollAreaVisible(false);
      
      // Use setTimeout to ensure DOM is fully rendered
      setTimeout(() => {
        if (scrollAreaRef.current) {
          // Set scroll position directly without animation
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
          hasInitialScrollRef.current = true;
          // Show the scroll area
          setScrollAreaVisible(true);
        }
      }, 0);
    }
  }, [contentReady]);

  // State for coach profile overlay
  const [showCoachOverlay, setShowCoachOverlay] = useState(false);

  // --------------------------------------------------
  // Data sharing preference handling
  // --------------------------------------------------
  const fetchDataSharingPref = useCallback(async () => {
    try {
      const enabled = await getDataSharingEnabledAction(Number(channelId), coachName);
      setDataSharingEnabled(enabled);
    } catch (err) {
      console.error("[DATA_SHARING] Failed to fetch preference", err);
      setDataSharingEnabled(false);
    }
  }, [channelId, coachName]);

  // Fetch on mount / when coach changes
  useEffect(() => {
    fetchDataSharingPref();
  }, [fetchDataSharingPref]);

  // Refetch when coach overlay closes (user may have toggled preference)
  useEffect(() => {
    if (!showCoachOverlay) {
      fetchDataSharingPref();
    }
  }, [showCoachOverlay, fetchDataSharingPref]);

  // --------------------------------------------------
  // Fetch coach details (type) for placeholder when empty
  // --------------------------------------------------
  useEffect(() => {
    let isMounted = true;
    (async () => {
      try {
        setCoachType(null);
        const details = await getCoachDetailsAction(coachName);
        if (isMounted) {
          setCoachType((details as any)?.type ?? null);
        }
      } catch (_err) {
        if (isMounted) setCoachType(null);
      }
    })();
    return () => {
      isMounted = false;
    };
  }, [coachName]);

  // State for interactive components
  const [showOptions, setShowOptions] = useState(false);

  // ---------- Coach thread overlay state ----------
  const [showCoachThread, setShowCoachThread] = useState(false);
  const [threadAnchorId, setThreadAnchorId] = useState<string | null>(null);
  const [highlightId, setHighlightId] = useState<string | null>(null);

  // Coach thread messages are filtered internally by CoachThread component

  const threadScrollRef = useRef<HTMLDivElement>(null);

  const openCoachThread = (clickedMessage?: any) => {
    setThreadAnchorId(clickedMessage ? clickedMessage.Id : null);
    setHighlightId(clickedMessage ? clickedMessage.Id : null);

    // Optimistically mark thread messages as seen in global state
    // Use functional update to avoid race conditions with recent message additions
    setAllMessages((currentMessages: any[]) => {
      return currentMessages.map((m: any) => (
        m.Type === "coach_message" && m.CoachName === coachName && m.Sender !== "user"
          ? { ...m, SeenByUser: 1 }
          : m
      ));
    });

    // Fire backend action (non-blocking)
    markCoachThreadMessagesSeenByUser(channelId, coachName).catch(err => console.error("Failed to mark thread messages seen", err));

    setShowCoachThread(true);

    if (clickedMessage) {
      setTimeout(() => setHighlightId(null), 2000);
    }
  };

  // Scroll to anchor when thread opens
  useEffect(() => {
    if (showCoachThread && threadScrollRef.current && threadAnchorId) {
      const el = threadScrollRef.current.querySelector(`[data-thread-id="${threadAnchorId}"]`) as HTMLElement | null;
      if (el) {
        el.scrollIntoView({ block: "center" });
      }
    }
  }, [showCoachThread, threadAnchorId]);
  

  // -------------------------------------------------------------
  // SAFETY: ensure the scroll area never remains hidden.
  // On some mobile transitions the previous logic could leave
  // `scrollAreaVisible` stuck at false (opacity 0), resulting in
  // an apparently empty chat history even though messages were
  // loaded correctly.  As soon as we have messages available we
  // force-show the scroll area.
  // -------------------------------------------------------------
  useEffect(() => {
    if (!scrollAreaVisible && allMessages.length > 0) {
      // Always reveal after the current tick to avoid fighting the
      // intentional short hide/show animations elsewhere.
      const t = setTimeout(() => setScrollAreaVisible(true), 0);
      return () => clearTimeout(t);
    }
  }, [allMessages, scrollAreaVisible]);

  const handleOpenCoachThread = () => {
    openCoachThread(); // Open without message
  }

  return (
    <div
      className="flex flex-col min-h-[100dvh] max-h-[100dvh] w-full relative pb-safe overflow-hidden"
      style={{
        opacity: 1,
        transition: 'none',
        height: '100dvh',
      }}
    >
      {/* Mobile Header with Back Button */}
      <div className="flex items-center p-3 border-b border-white/10 bg-black/20 backdrop-blur-sm">
        {isMobile && (
          <Button
            variant="ghost"
            onClick={onBack}
            css="p-2 text-white hover:bg-white/10 rounded-md mr-3"
          >
            <BackIcon className="w-5 h-5" />
          </Button>
        )}
        <div className="flex items-center gap-3 cursor-pointer" onClick={() => setShowCoachOverlay(true)}>
          <img
            src={`https://storage.googleapis.com/awaken-audio-files/${coachName.toLowerCase().replace(/\s+/g, '-')}-portrait.png`}
            alt={coachName}
            className="w-8 h-8 rounded-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/default-coach-avatar.png';
            }}
          />
          <Text className="text-base font-semibold text-white">{coachName}</Text>
        </div>

        {/* Coach Thread Button - Top Right (mobile) */}
        {onboarded && !fullscreenMode && !isVoiceCall && dataSharingEnabled && (
          <div className="absolute top-3 right-3 z-30">
            <Button
              variant="ghost"
              onClick={handleOpenCoachThread}
              css={`p-2 !text-[#FCA311] hover:bg-transparent active:bg-transparent relative ${hasUnseenCoachMessages ? 'animate-pulse-glow' : ''}`}
              style={{ WebkitTapHighlightColor: 'transparent' }}
            >
              <ThreadIcon className="w-5 h-5" />
            </Button>
          </div>
        )}
      </div>
      {/* Add the styles */}
      <style>{`
        ${loadingOverlayStyles(contentReady)}
        ${keyboardStyles}
      `}</style>
      
      {/* Chat messages area - Always visible in new design with optimized spacing */}
      <div className="flex-1 overflow-hidden bg-transparent w-full px-4 py-1 mb-1 z-10 min-h-0">
        {/* Add separator after header */}
        {fullscreenMode && (
          <div className="message-separator mb-3" />
        )}
        
        <div className="relative h-full">
          <ScrollArea
            className="max-h-[calc(100vh-120px)] min-h-[calc(100vh-140px)] overflow-x-hidden h-full"
            ref={scrollAreaRef}
            onWheel={handleScrollIntent}
            onTouchStart={handleScrollIntent}
            onTouchMove={handleScrollIntent}
            style={{
              opacity: scrollAreaVisible ? 1 : 0,
              transition: scrollAreaVisible ? 'opacity 0.1s ease' : 'none'
            }}
          >
            {/* Loading indicator overlay */}
            {loadingMore && (
              <div className="loading-overlay absolute top-0 w-full text-center bg-black bg-opacity-50 py-1 z-10">
                <Text css="text-gray-400 text-xs">Getting messages...</Text>
              </div>
            )}
            <div className="space-y-4 mb-2 w-full pb-2">
              {filteredMessages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full min-h-[500px] text-center">
                  <div className="mx-auto w-full max-w-[85%]">
                    {coachType && (
                      <Text className="block text-white/80 font-light whitespace-pre-wrap mb-4">
                        {coachType}
                      </Text>
                    )}
                    <Text className="block text-white/50 text-center italic font-light">
                      Tap on {coachName}'s name<br />to see their full profile
                    </Text>
                  </div>
                </div>
              ) : (
                filteredMessages.map((message, index) => {
                  // Find the actual index of this message in the full allMessages array
                  const actualIndex = allMessages.findIndex(m => m.Id === message.Id);
                  return (
                    <MessageItem
                      key={message.Id}
                      message={message}
                      index={actualIndex}
                      allMessages={allMessages}
                      contentReady={contentReady}
                      currentLetterIndex={currentLetterIndex}
                      activeAudioId={activeAudioId}
                      setActiveAudioId={setActiveAudioId}
                      toggleStar={toggleStar}
                      showCoachPill={true}
                      onCoachMessageClick={(msg) => openCoachThread(msg)}
                    />
                  );
                })
              )}
            </div>
          </ScrollArea>

          {/* Fade effects for top and bottom */}
          <div className={`fade-bottom ${showFadeBottom ? 'fade-visible' : ''}`}></div>
          
          {/* Scroll down button */}
          {showFadeBottom && !isAtBottom && (
            <div className="absolute bottom-4 right-0 left-0 flex justify-center pointer-events-none z-30">
              <Button
                variant="outline"
                css="rounded-full p-2 bg-[#26262699] backdrop-blur-sm border border-[#FCA31166] hover:bg-[#262626] hover:border-[#FCA311] pointer-events-auto shadow-lg"
                onClick={() => {
                  if (scrollAreaRef.current) {
                    scrollAreaRef.current.scrollTo({
                      top: scrollAreaRef.current.scrollHeight,
                      behavior: "smooth",
                    });
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-[#FCA311]">
                  <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-.53 14.03a.75.75 0 001.06 0l3-3a.75.75 0 10-1.06-1.06l-1.72 1.72V8.25a.75.75 0 00-1.5 0v5.69l-1.72-1.72a.75.75 0 00-1.06 1.06l3 3z" clipRule="evenodd" />
                </svg>
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* Input section */}
      <div className="w-full mb-2 px-2 z-20">
          <ChatInput
            loading={loading}
            input={input}
            setInput={setInput}
            sendMessage={(input: string | Blob) => sendMessage(input, false, false)}
            placeholder={`Message ${coachName}`}
            channelId={channelId.toString()}
          showOptionsButton={!showOptions && onboarded && !input.trim()}
          onOptionsClick={() => setShowOptions(true)}
          onVoiceCallClick={() => toggleVoiceCall()}
          />
      </div>

      

      {/* Options Component - always mounted so the voice note overlay can remain visible
          even after the quick-actions panel is closed. Visibility of the panel itself is
          controlled by the `isVisible` prop. */}
      <Options
        isVisible={showOptions}
        onClose={() => setShowOptions(false)}
        coachName={coachName}
        channelId={channelId.toString()}
        user={user}
        sendMessage={sendMessage}
        toggleVoiceCall={toggleVoiceCall}
      />



      {/* Coach Details Overlay */}
      <CoachOverlay
        isVisible={showCoachOverlay}
        coachName={coachName}
        onClose={() => setShowCoachOverlay(false)}
        channelId={channelId}
      />

      {/* ---------- Coach Thread Overlay ---------- */}
      {/* Coach Thread Overlay */}
      <CoachThread
        visible={showCoachThread}
        onClose={() => setShowCoachThread(false)}
        allMessages={allMessages}
        initialMessageId={threadAnchorId}
        coachName={coachName}
        channelId={channelId}
        setAllMessages={setAllMessages}
      />
    </div>
  );
};
