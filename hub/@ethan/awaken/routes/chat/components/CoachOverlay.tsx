"use client";

import React, { useEffect, useState } from "npm:react@canary";
import { getCoachDetailsAction, getDataSharingEnabledAction, setDataSharingEnabledAction } from "../../../actions/db/coach-actions.ts";
import { animated, useSpring } from "npm:@react-spring/web";
import { getHumanCoachName, isAIOnlyCoach } from "./coach-utils.ts";
import { EssenceIcon } from "../../../lib/icons.tsx";

interface CoachDetailsType {
  name: string;
  description: string | null;
  type: string | null;
  metadata: any;
  costMultiplier?: number;
  essencePerMinute?: number;
  essencePerMessage?: number;
}

interface CoachOverlayProps {
  isVisible: boolean;
  coachName: string | null;
  onClose: () => void;
  channelId: string | number;
}

// Helper to build portrait URL identical to other components
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

export const CoachOverlay = ({
  isVisible,
  coachName,
  onClose,
  channelId,
}: CoachOverlayProps): React.ReactNode => {
  const [coachDetails, setCoachDetails] = useState<CoachDetailsType | null>(null);
  const [loading, setLoading] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [dataSharingEnabled, setDataSharingEnabled] = useState(false);

  // Animation for fade in/out only (no sliding)
  const overlayAnimation = useSpring({
    opacity: isVisible ? 0.95 : 0,
    config: isVisible 
      ? { tension: 280, friction: 60 } // Opening animation (normal)
      : { tension: 350, friction: 40 },  // Faster closing animation
    delay: 0, // Removed delay for closing animation
    onRest: () => {
      // Clear coach details when overlay is fully hidden
      if (!isVisible) {
        setMounted(false);
        setCoachDetails(null);
      }
    }
  });

  // Content animation with slight delay
  const contentAnimation = useSpring({
    opacity: isVisible ? 1 : 0,
    transform: isVisible ? 'translateY(0px)' : 'translateY(10px)',
    delay: isVisible ? 100 : 0,
    config: isVisible
      ? { tension: 280, friction: 60 } // Normal opening
      : { tension: 350, friction: 40 }  // Faster closing
  });

  // Keep component mounted during exit animation
  useEffect(() => {
    if (isVisible) {
      setMounted(true);
    }
  }, [isVisible]);

  // Fetch coach details when overlay becomes visible
  useEffect(() => {
    if (isVisible && coachName) {
      const fetchCoachDetails = async () => {
        setLoading(true);
        try {
          const details = await getCoachDetailsAction(coachName);
          setCoachDetails(details);
        } catch (error) {
          console.error("Error fetching coach details:", error);
        } finally {
          setLoading(false);
        }
      };
      
      fetchCoachDetails();
    }
  }, [isVisible, coachName]);

  // Fetch data-sharing preference when overlay becomes visible
  useEffect(() => {
    if (isVisible && coachName && channelId) {
      (async () => {
        try {
          const enabled = await getDataSharingEnabledAction(Number(channelId), coachName);
          setDataSharingEnabled(enabled);
        } catch (err) {
          console.error("Error fetching data-sharing preference", err);
        }
      })();
    }
  }, [isVisible, coachName, channelId]);

  // Use precomputed cost rates from server
  const essencePerMinute = coachDetails?.essencePerMinute?.toFixed(1);
  const essencePerMessage = coachDetails?.essencePerMessage?.toFixed(1);

  const handleToggleDataSharing = async () => {
    const next = !dataSharingEnabled;
    setDataSharingEnabled(next);
    try {
      await setDataSharingEnabledAction(Number(channelId), coachName || "", next);
    } catch (err) {
      console.error("Error updating data-sharing preference", err);
    }
  };

  // Close on backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking directly on the backdrop
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isVisible && !mounted) return null;

  return (
    <>
      {/* Full-screen invisible background for click handler - only show when visible */}
      {isVisible && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={handleBackdropClick}
        />
      )}

      {/* Animated overlay */}
      <animated.div 
        className="fixed inset-0 flex flex-col"
        style={{
          ...overlayAnimation,
          background: 'rgba(0, 0, 0, 0.95)',
          backdropFilter: 'blur(4px)',
          pointerEvents: isVisible ? 'auto' : 'none',
          zIndex: 60 // above header & backdrop
        }}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-amber-300 focus:outline-none z-[100] pointer-events-auto"
          aria-label="Close"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
            <path fillRule="evenodd" d="M6.225 5.47a.75.75 0 011.06 0L12 10.185l4.715-4.715a.75.75 0 111.06 1.06L13.06 11.25l4.715 4.715a.75.75 0 11-1.06 1.06L12 12.31l-4.715 4.715a.75.75 0 11-1.06-1.06l4.715-4.715-4.715-4.715a.75.75 0 010-1.06z" clipRule="evenodd" />
          </svg>
        </button>

        {/* Scrollable content (everything except the ✕ button) */}
        <div
          className="flex-1 overflow-y-auto overscroll-contain px-6 pt-[77px] pb-12 w-full max-w-md mx-auto"
          style={{ WebkitOverflowScrolling: 'touch', minHeight: 0 }}
        >
          {/* Coach portrait */}
          {coachName && (
            <img
              src={getCoachPortraitUrl(coachName)}
              alt={`${coachName} portrait`}
              className="mx-auto w-[126px] h-[126px] rounded-full object-cover border-2 border-amber-500/60 shadow-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/default-coach-avatar.png';
              }}
            />
          )}

          {/* Coach name */}
          {!loading && coachDetails && (
            <animated.div 
              className="mt-4 mb-8 text-center"
              style={contentAnimation}
            >
              <div className="text-[#FCA311] text-2xl font-medium">
                {coachDetails.name}
              </div>
            </animated.div>
          )}

          <animated.div 
            className="space-y-6 text-center"
            style={contentAnimation}
          >
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : coachDetails && essencePerMinute && essencePerMessage ? (
              <>
                {coachDetails.type && (
                  <div className="uppercase tracking-wider font-light text-orange-300 text-sm whitespace-pre-wrap">
                    {coachDetails.type}
                  </div>
                )}

                  
                {/* Data-sharing toggle */}
                {coachDetails && (
                  <div className="my-8 flex items-center justify-between bg-black border border-[#FCA311] rounded-2xl px-6 py-4 text-white">
                    <span className="text-base text-left flex-1 pr-4">
                      {isAIOnlyCoach(coachDetails.name)
                        ? "Let Awaken's creators see our chats and message me"
                        : `Let the actual ${getHumanCoachName(coachDetails.name)} see my chats and message me`}
                    </span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={dataSharingEnabled}
                        onChange={handleToggleDataSharing}
                        className="sr-only peer"
                      />
                      <div className="w-14 h-8 bg-gray-700 rounded-full peer peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-amber-400 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-1 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all dark:border-gray-600 peer-checked:bg-[#FCA311]"></div>
                    </label>
                  </div>
                )}

                {/* Rate info - compact, slightly larger two-line pills */}
                <div className="mt-2 flex flex-col items-center gap-2">
                  <div className="inline-flex items-center gap-2 rounded-full bg-white/5 px-4 py-1.5 text-white">
                    <span className="text-base font-semibold">{essencePerMinute}</span>
                    <div className="text-[#ff6b35]">
                      <EssenceIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm text-white/80 tracking-wide">PER MINUTE</span>
                  </div>
                  <div className="inline-flex items-center gap-2 rounded-full bg-white/5 px-4 py-1.5 text-white">
                    <span className="text-base font-semibold">{essencePerMessage}</span>
                    <div className="text-[#ff6b35]">
                      <EssenceIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm text-white/80 tracking-wide">PER MESSAGE</span>
                  </div>
                </div>


                <div className="w-16 h-0.5 bg-orange-500/30 mx-auto"></div>

                <p className="font-light leading-relaxed text-white whitespace-pre-wrap text-left">
                  {coachDetails.description || "No description available."}
                </p>

                {coachDetails.metadata?.specialties && (
                  <section>
                    <div className="font-medium text-orange-200 text-sm mb-3">
                      SPECIALTIES
                    </div>
                    <div className="flex flex-wrap justify-center gap-2 mt-2">
                      {coachDetails.metadata.specialties.map((specialty: string, index: number) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-orange-900/40 text-orange-200 rounded-full text-xs border border-orange-700/30"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </section>
                )}
              </>
            ) : (
              <p className="text-white">No information available</p>
            )}
          </animated.div>
        </div>
      </animated.div>
    </>
  );
}; 