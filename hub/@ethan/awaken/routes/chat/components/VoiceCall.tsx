"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import {
  <PERSON><PERSON>,
  Lo<PERSON>,
} from "@reframe/ui/main.tsx";

import {
  animated,
  useSpring,
} from "npm:@react-spring/web";

import Vapi from "npm:@vapi-ai/web";

import { FireSvg } from "./FireSvg.tsx";
import { checkCallLimit, waitForCallProcessingComplete } from "../../../action.ts";
import { trackCall, getVapiConfig, storeCallControlUrl } from "../../../actions/ai/call-actions.ts";
import { MicMutedIcon } from "../../../lib/icons.tsx";
import { MicIcon } from "@reframe/icons/mic.ts";
import { Logo } from "../../../lib/logo.tsx";
import { Circle } from "./Circle.tsx";
import { Layout } from "../../../lib/layout.tsx";
import { isMobile } from "../../../lib/platform-utils.ts";

const vapiClient = new Vapi("c10a529d-0412-44ed-849d-5c2c75b6f460");

// Function to convert coach name to portrait image URL
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

interface VoiceCallProps {
  user: any;
  coachName: string;
  onCallEnd: () => void;
  onPaymentOpen: () => void;
  onTopUpOpen: () => void;
  setAllMessages: (messages: any) => void;
  goalInput?: string;
  cardContext?: any;
  audio: any;
  rootAudioRef: React.RefObject<HTMLAudioElement>;
  setIsPlaying: (playing: boolean) => void;
  resetOutputSentence: () => void;
  setOutput: (output: string) => void;
  originalOutput: string;
  setIsComplete: (complete: boolean) => void;
  stopAudio: () => void;
  audioScale: number;
}

export const VoiceCall = ({
  user,
  coachName,
  onCallEnd,
  onPaymentOpen,
  onTopUpOpen,
  setAllMessages,
  goalInput,
  cardContext,
  audio,
  rootAudioRef,
  setIsPlaying,
  resetOutputSentence,
  setOutput,
  originalOutput,
  setIsComplete,
  stopAudio,
  audioScale
}: VoiceCallProps) => {
  const [isMuted, setIsMuted] = useState(false);
  const [callStarted, setCallStarted] = useState(false);
  const [callEnded, setCallEnded] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [maxCallDuration, setMaxCallDuration] = useState(0);
  const [polling, setPolling] = useState(false);
  const [assistantVolume, setAssistantVolume] = useState(0);
  const [userVolume, setUserVolume] = useState(0);
  const [isCoachSpeaking, setIsCoachSpeaking] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showEarphoneMessage, setShowEarphoneMessage] = useState(false);
  const [showQuietPlaceMessage, setShowQuietPlaceMessage] = useState(false);
  const [ellipsisCount, setEllipsisCount] = useState(0);
  const currentCallId = useRef<string | null>(null);
  const isCancelledRef = useRef(false);
  
  // Single ref for user volume detection
  const userVolumeRef = useRef<{
    stream?: MediaStream;
    audioContext?: AudioContext;
    analyser?: AnalyserNode;
    animationFrame?: number;
  }>({});

  // Ref for the speech end timer
  const speechEndTimerRef = useRef<number | null>(null);

  // Start call when component mounts
  useEffect(() => {
    // Show quiet place message immediately
    setShowQuietPlaceMessage(true);
    // Hide after 3 seconds
    const timer = setTimeout(() => {
      setShowQuietPlaceMessage(false);
    }, 3000);
    
    checkCallLimitAndStartCall();
    
    return () => clearTimeout(timer);
  }, []);

  // Extract call limit checking and starting logic
  const checkCallLimitAndStartCall = async (isFirst?: boolean) => {
    if (isCancelledRef.current) return;
    
    setLoading(true);
    try {
      const checkData = await checkCallLimit(user.channelId);

      if (isCancelledRef.current) return;

      if (!checkData.allowed) {
        if(checkData.type === "monthly_call_limit_reached") {
          if(checkData.action === "checkout") onPaymentOpen();
          else if(checkData.action === "top_up_xyz") onTopUpOpen();
          
          setLoading(false);
          onCallEnd();
          return;
        }
        
        if (checkData.type === "daily_call_limit_reached") {
          onPaymentOpen();
          setLoading(false);
          onCallEnd();
          return;
        }
        console.error("Error checking call limit:", checkData.error);
        setLoading(false);
        onCallEnd();
        return;
      }

      setMaxCallDuration(checkData.maxCallDuration || 0);
      // Start the call setup
      try {
        const vapiAssistantData = await getVapiConfig(
          user.channelId,
          isFirst || false,
          cardContext?.goalInput || goalInput || null,
          coachName,
          cardContext
        );

        if (isCancelledRef.current) return;

        if (!vapiAssistantData) {
          console.error("Failed to get Vapi assistant configuration.");
          onCallEnd();
          setLoading(false);
          return;
        }
        
        const vapiClientResponse = await vapiClient.start(vapiAssistantData.assistantId);
        
        if (isCancelledRef.current) {
          // If cancelled after starting, stop the call
          await vapiClient.stop();
          return;
        }
        
        // Store the control URL associated with the call ID (non-blocking)
        if (vapiClientResponse?.id && vapiClientResponse?.monitor?.controlUrl) {
          storeCallControlUrl(user.channelId, vapiClientResponse.id, vapiClientResponse.monitor.controlUrl)
            .catch(err => console.error("Error storing control URL:", err));
        } else {
          console.error("Could not store control URL: Missing id or monitor.controlUrl in vapiClientResponse");
        }
        
        // Send call tracking info to server (non-blocking)
        if (vapiClientResponse?.id && vapiClientResponse?.monitor?.controlUrl) {
          trackCall(user.channelId, vapiClientResponse.id, vapiClientResponse.monitor.controlUrl, vapiAssistantData.assistantId, coachName).catch(err => {
            console.error("Error notifying server about call start:", err);
          });
        } else {
           console.error("Could not track call: Missing id or monitor.controlUrl in vapiClientResponse");
        }
        
        if (!isCancelledRef.current) {
          setCallStarted(true);
          currentCallId.current = vapiClientResponse?.id;
          console.log("Current call ID:", currentCallId.current);
        }
      } catch (error) {
        console.error("Error starting call:", error);
        if (!isCancelledRef.current) {
          onCallEnd();
        }
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error("Error in checkCallLimitAndStartCall:", error);
      if (!isCancelledRef.current) {
        onCallEnd();
      }
      setLoading(false);
    }
  };

  const pollForResponse = async () => {
    try {
      console.log("Starting long poll for call processing completion...");
      if (!currentCallId.current) {
        console.error("No call ID available for polling");
        setPolling(false);
        onCallEnd();
        setCallEnded(false);
        return;
      }
      
      const result = await waitForCallProcessingComplete(currentCallId.current, user.channelId);
      console.log("Long poll response:", result);
      
      if (result?.success) {
        console.log("Call processing completed, updating chat view.");
        setAllMessages(result.messages);
        console.log("Call ended");
        setPolling(false);
        onCallEnd();
        setCallEnded(false);
      } else {
        console.error("Call processing failed or timed out:", result?.error);
        setPolling(false);
        onCallEnd();
        setCallEnded(false);
      }
    } catch (error) {
      console.error("Error during long poll:", error);
      setPolling(false);
      onCallEnd();
      setCallEnded(false);
    }
  };

  const formatTime = (seconds: number) => {
    const hrs = Math.floor(seconds / 3600)
      .toString()
      .padStart(2, "0");
    const mins = Math.floor((seconds % 3600) / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${hrs}:${mins}:${secs}`;
  };

  // Vapi event handlers
  useEffect(() => {
    const handleCallStart = () => {
      console.log("Call has started");
      if (!callStarted) {
        setCallStarted(true);
      }
    };

    vapiClient.on("call-start", handleCallStart);

    return () => {
      vapiClient.off("call-start", handleCallStart);
    };
  }, [vapiClient, callStarted]);

  useEffect(() => {
    const handleVolumeLevel = (volume: number) => {
      setAssistantVolume(volume);
    };

    vapiClient.on("volume-level", handleVolumeLevel);

    return () => {
      vapiClient.off("volume-level", handleVolumeLevel);
    };
  }, [vapiClient]);

  useEffect(() => {
    const handleCallEnd = async () => {
      console.log("SUPPOSED TO BE HERE");
      console.log("Call has ended");

      if (!callEnded) {
        console.log("LETS IS IF HERE");
        setCallEnded(true);
        setCallStarted(false);
        setPolling(true);
        await pollForResponse();
      }
    };

    vapiClient.on("call-end", handleCallEnd);

    return () => {
      vapiClient.off("call-end", handleCallEnd);
    };
  }, [vapiClient, callEnded, user.channelId]);

  useEffect(() => {
    const handleSpeechStart = () => {
      setIsCoachSpeaking(true);
      if (speechEndTimerRef.current) {
        clearTimeout(speechEndTimerRef.current);
        speechEndTimerRef.current = null;
      }
    };
    const handleSpeechEnd = () => {
      speechEndTimerRef.current = window.setTimeout(() => {
        setIsCoachSpeaking(false);
      }, 750); // Adjust delay as needed
    };

    vapiClient.on("speech-start", handleSpeechStart);
    vapiClient.on("speech-end", handleSpeechEnd);

    return () => {
      vapiClient.off("speech-start", handleSpeechStart);
      vapiClient.off("speech-end", handleSpeechEnd);
      if (speechEndTimerRef.current) {
        clearTimeout(speechEndTimerRef.current);
      }
    };
  }, [vapiClient]);

  // Reset the timer whenever a fresh call actually starts
  useEffect(() => {
    if (callStarted) {
      setCallDuration(0);
      
      // Show earphone message on mobile after 3 seconds
      if (isMobile()) {
        const showTimer = setTimeout(() => {
          setShowEarphoneMessage(true);
          // Hide after 3 seconds
          const hideTimer = setTimeout(() => {
            setShowEarphoneMessage(false);
          }, 3000);
        }, 3000);
        
        return () => {
          clearTimeout(showTimer);
        };
      }
    }
  }, [callStarted]);

  // Call duration timer that pauses during polling but does **not** reset
  useEffect(() => {
    let intervalId: number | null = null;
    if (callStarted && !polling) {
      intervalId = window.setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => {
      if (intervalId) window.clearInterval(intervalId);
    };
  }, [callStarted, polling]);

  // Minimal user volume detection
  const startUserVolumeDetection = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      userVolumeRef.current.stream = stream;
      
      const audioContext = new (globalThis.AudioContext || (window as any).webkitAudioContext)();
      userVolumeRef.current.audioContext = audioContext;
      
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256; // Smaller for performance
      analyser.smoothingTimeConstant = 0.8;
      userVolumeRef.current.analyser = analyser;
      
      audioContext.createMediaStreamSource(stream).connect(analyser);
      
      // Start monitoring loop
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const updateVolume = () => {
        if (!userVolumeRef.current.analyser) return;
        
        userVolumeRef.current.analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;
        setUserVolume(average / 255);
        
        userVolumeRef.current.animationFrame = requestAnimationFrame(updateVolume);
      };
      updateVolume();
    } catch (error) {
      console.error("Error accessing microphone:", error);
    }
  };
  
  const stopUserVolumeDetection = () => {
    const { animationFrame, stream, audioContext } = userVolumeRef.current;
    
    if (animationFrame) cancelAnimationFrame(animationFrame);
    if (stream) stream.getTracks().forEach(track => track.stop());
    if (audioContext) audioContext.close();
    
    userVolumeRef.current = {};
    setUserVolume(0);
  };

  // Start user volume detection when call starts
  useEffect(() => {
    if (callStarted && !isMuted) {
      startUserVolumeDetection();
    } else {
      stopUserVolumeDetection();
    }
    
    return () => {
      stopUserVolumeDetection();
    };
  }, [callStarted, isMuted]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      stopUserVolumeDetection();
    };
  }, []);

  // Spring animation for earphone message
  const earphoneMessageSpring = useSpring({
    opacity: showEarphoneMessage ? 1 : 0,
    config: { duration: 500 }
  });

  // Spring animation for quiet place message
  const quietPlaceMessageSpring = useSpring({
    opacity: showQuietPlaceMessage ? 1 : 0,
    config: { duration: 500 }
  });

  // Subtle float/fade loop for the reviewing text
  const reviewingSpring = useSpring({
    from: { opacity: 0.6, transform: "translateY(2px)" },
    to: { opacity: 1, transform: "translateY(0px)" },
    loop: { reverse: true },
    pause: !polling,
    config: { duration: 1200 }
  });

  // Animated ellipsis while polling
  useEffect(() => {
    if (!polling) {
      setEllipsisCount(0);
      return;
    }
    const id = window.setInterval(() => {
      setEllipsisCount((prev) => (prev >= 3 ? 0 : prev + 1));
    }, 450);
    return () => window.clearInterval(id);
  }, [polling]);

  return (
    <Layout user={user} keepDrawer={false}>
    <animated.div
      className="relative flex items-center justify-center min-h-dvh text-orange-300 p-6 component-b overflow-hidden"
    >
      {/* Title (Top-Left Corner) */}
      <div className="absolute top-4 left-4 flex items-center gap-2 z-20">
        <Logo size={20} />
        <span className="text-base font-normal text-white">awaken</span>
      </div>

      {/* X button (Top-Right Corner) */}
      {!polling && !callStarted && (
        <div className="absolute top-4 right-4 z-20">
          <Button
            variant="outline"
            css="
              w-10 h-10 p-0
              rounded-full border-none bg-[#95959526]
              hover:bg-[#5C3B0633] hover:border-[#FCA311]
              flex items-center justify-center
            "
            onClick={async () => {
              isCancelledRef.current = true;
              if (vapiClient) {
                await vapiClient.stop();
              }
              onCallEnd();
            }}
          >
            <span className="text-white text-lg">×</span>
          </Button>
        </div>
      )}

      {/* Text elements above the Circle and FireSvg */}
      <div className="absolute top-20 flex flex-col items-center space-y-2 mb-12 z-10">
        {/* Show coach name only when loaded */}
        <h3 className="text-2xl font-normal text-white">
          {coachName ? coachName : <Loader size="sm" />}
        </h3>
        <p className="text-sm">
          <span className="text-white">
            {callStarted || polling
              ? `${Math.floor(callDuration / 60)}:${(callDuration % 60)
                  .toString()
                  .padStart(2, "0")}`
              : "a moment to connect..."}
          </span>
          {(callStarted || polling) && maxCallDuration > 0 && (
            <span className="text-gray-500"> / {maxCallDuration}:00</span>
          )}
        </p>
      </div>

      {/* Centered Circle and FireSvg */}
      <animated.div
        style={{ y: 0 }}
        className="relative flex flex-col items-center justify-center z-10 text-[#FCA311] items-center gap-8"
      >
        <Circle
          loading={!callStarted}
          audio={audio}
          size="180px"
          isCall={callStarted}
          isPlaying={false}
          isCoachSpeaking={isCoachSpeaking}
          rootAudioRef={rootAudioRef}
          setIsPlaying={setIsPlaying}
          resetOutputSentence={resetOutputSentence}
          setOutput={setOutput}
          originalOutput={originalOutput}
          setIsComplete={setIsComplete}
          stopAudio={stopAudio}
          initAudio={null}
          audioScale={audioScale}
          assistantVolume={assistantVolume}
          userVolume={userVolume}
        />
        <FireSvg
          loading={!callStarted}
          height="250"
          className="inset-0 z-[-10]"
          isExpanded={false}
        />
        {/* Conditionally render "Listening" text below Circle/Fire */}
        {callStarted && !isCoachSpeaking && (
          <p className="text-[#FCA311] text-lg mt-4 absolute bottom-[-30px]">Listening</p>
        )}
        {/* Conditionally render animated "reviewing session" text below Circle/Fire */}
        {polling && (
          <animated.p
            style={reviewingSpring}
            className="text-[#FCA311] text-lg mt-4 absolute bottom-[-30px] left-1/2 -translate-x-1/2"
          >
            <span className="relative inline-block">
              reviewing session
              <span
                aria-hidden
                className="absolute left-full ml-1 inline-block"
                style={{ width: "3ch" }}
              >
                <span style={{ opacity: ellipsisCount >= 1 ? 1 : 0.25, transition: "opacity 280ms ease" }}>.</span>
                <span style={{ opacity: ellipsisCount >= 2 ? 1 : 0.25, transition: "opacity 280ms ease" }}>.</span>
                <span style={{ opacity: ellipsisCount >= 3 ? 1 : 0.25, transition: "opacity 280ms ease" }}>.</span>
              </span>
            </span>
          </animated.p>
        )}
        {/* Quiet place message */}
        <animated.p 
          style={quietPlaceMessageSpring}
          className="text-[#FCA311] text-sm mt-4 absolute bottom-[-90px] pointer-events-none"
        >
          Please find a quiet place
        </animated.p>
        {/* Earphone connection message */}
        <animated.p 
          style={earphoneMessageSpring}
          className="text-[#FCA311] text-sm mt-4 absolute bottom-[-60px] pointer-events-none"
        >
          Earphones may now be connected
        </animated.p>
      </animated.div>

      {/* Mute/unmute mic button pinned to bottom-left */}
      <div className="absolute bottom-4 left-4 z-30 pointer-events-auto">
        <Button
          variant="outline"
          css="
            w-12 p-1 md:p-3 h-auto
            rounded-full border-none bg-[#95959526]
            hover:bg-[#5C3B0633] hover:border-[#FCA311]
            flex items-center justify-center
          "
          onClick={() => {
            setIsMuted(!isMuted);
            vapiClient.setMuted(!isMuted);
          }}
          disabled={!callStarted || polling}
        >
        {isMuted ? (
          <MicMutedIcon className="w-6 h-6 text-white" />
        ) : (
          <MicIcon className="w-6 h-6 text-white" />
        )}
        </Button>
      </div>

      {/* End button centered at the bottom - only show when call is active */}
      {callStarted && !polling && (
        <div className="absolute bottom-4 left-0 right-0 px-4 z-20 flex items-center justify-center">
          <Button
            onClick={async () => {
              console.log(user.channelId);
              setCallStarted(false);
              setPolling(true);
              if (vapiClient) {
                await vapiClient.stop();
              }
            }}
            css="flex gap-2 items-center bg-red-600
              text-white rounded-lg hover:bg-red-700
              transition-colors text-center
            "
          >
            End
          </Button>
        </div>
      )}
    </animated.div>
    </Layout>
  );
};