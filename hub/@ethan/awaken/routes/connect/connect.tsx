"use client";

import React, { useEffect, useState } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { Layout } from "../../lib/layout.tsx";
import { Logo } from "../../lib/logo.tsx";
import { getCoachNameFromSlugAction, connectUserToCoachAction, getCoachDetailsAction, getDataSharingEnabledAction, setDataSharingEnabledAction } from "../../actions/db/coach-actions.ts";
import { animated, useSpring } from "npm:@react-spring/web";
import { getHumanCoachName } from "../chat/components/coach-utils.ts";

interface ConnectPageProps {
  user: any | null;
  userData: any | null;
  enrollmentMessage?: string | null;
  isLoadingEnrollment?: boolean;
  coachSlug: string;
}

interface CoachDetailsType {
  name: string;
  description: string | null;
  type: string | null;
  metadata: any;
}

export const ConnectPage = ({
  user,
  userData,
  enrollmentMessage,
  isLoadingEnrollment = false,
  coachSlug
}: ConnectPageProps): React.ReactNode => {
  const [coachDetails, setCoachDetails] = useState<CoachDetailsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [dataSharingEnabled, setDataSharingEnabled] = useState(true);

  // Animation for the main content
  const contentAnimation = useSpring({
    opacity: loading ? 0 : 1,
    transform: loading ? 'translateY(20px)' : 'translateY(0px)',
    config: { tension: 280, friction: 60 }
  });

  // Function to convert coach name to portrait image URL
  const getCoachPortraitUrl = (coachName: string): string => {
    const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
    return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
  };

  // Fetch coach details on mount
  useEffect(() => {
    const fetchCoachDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Convert slug to coach name
        const coachName = await getCoachNameFromSlugAction(coachSlug);
        
        if (!coachName) {
          setError("Coach not found");
          setLoading(false);
          return;
        }

        // Get coach details
        const details = await getCoachDetailsAction(coachName);
        setCoachDetails(details);
      } catch (err) {
        console.error("Error fetching coach details:", err);
        setError("Failed to load coach details");
      } finally {
        setLoading(false);
      }
    };

    fetchCoachDetails();
  }, [coachSlug]);

  // Determine whether the currently signed-in user already has access to this coach
  const isAlreadyConnected = React.useMemo(() => {
    if (!user || !userData || !coachDetails) return false;

    try {
      const accessRaw = userData.coachAccess ?? "{}";
      const accessObj = typeof accessRaw === "string" ? JSON.parse(accessRaw) : accessRaw;
      return !!accessObj[coachDetails.name];
    } catch {
      return false;
    }
  }, [user, userData, coachDetails]);

  // Handle data sharing toggle
  const handleToggleDataSharing = () => {
    setDataSharingEnabled(!dataSharingEnabled);
  };

  // Handle connecting to the coach (or redirecting to sign-in when anonymous)
  const handleConnect = async () => {
    // If the user is not logged-in, take them to sign-in and include the coachSlug so we can finish the flow later.
    if (!user || !user.channelId) {
      try {
        // Ensure server can detect connect-flow on the auth callback even if SSR cookie set fails
        document.cookie = `connectFlow=1; path=/; max-age=600; samesite=Lax`;
        document.cookie = `connectCoachSlug=${encodeURIComponent(coachSlug)}; path=/; max-age=600; samesite=Lax`;
      } catch {}
      window.location.href = `/auth/sign-in?coachSlug=${coachSlug}&dataSharingEnabled=${dataSharingEnabled}`;
      return;
    }

    // If already connected do nothing.
    if (isAlreadyConnected) return;

    if (!coachDetails) return;

    try {
      setConnecting(true);
      setError(null);

      const result = await connectUserToCoachAction(user.channelId.toString(), coachDetails.name);

      if (result.success) {
        setSuccess(true);
        // Redirect to chat after a short delay
        setTimeout(() => {
          window.location.href = "/chat";
        }, 2000);
      } else {
        setError(result.error || "Failed to connect to coach");
      }
    } catch (err) {
      console.error("Error connecting to coach:", err);
      setError("An unexpected error occurred");
    } finally {
      setConnecting(false);
    }
  };

  // Automatically connect after returning from sign-in when the URL contains ?auto=1
  useEffect(() => {
    if (!user || !coachDetails) return;

    if (typeof window === "undefined") return;

    const params = new URLSearchParams(window.location.search);
    const autoParam = params.get("auto");
    const dataSharingParam = params.get("dataSharingEnabled");

    if (autoParam === "1" && !isAlreadyConnected && !success && !connecting) {
      handleConnect().then(() => {
        // Apply data sharing preference after successful connection
        if (dataSharingParam && user.channelId && coachDetails.name) {
          const enableSharing = dataSharingParam === "true";
          setDataSharingEnabledAction(Number(user.channelId), coachDetails.name, enableSharing)
            .then(() => {
              console.log(`[CONNECT] Applied data sharing preference: ${enableSharing} for coach ${coachDetails.name}`);
            })
            .catch((error) => {
              console.error("[CONNECT] Error applying data sharing preference:", error);
            });
        }
      });
    }
  }, [user, coachDetails, isAlreadyConnected, success, connecting]);

  return (
    <Layout 
      user={user ?? {}} 
      userData={userData} 
      enrollmentMessage={enrollmentMessage}
      isLoadingEnrollment={isLoadingEnrollment}
      keepDrawer={false}
      currentPath="connect"
    >
      <div className="relative flex flex-col min-h-svh max-h-svh bg-transparent overflow-y-auto">
        <div className="flex-1 flex flex-col items-center justify-center px-6 py-8">
          {loading ? (
            <div className="flex justify-center items-center">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : error ? (
            <animated.div 
              className="bg-black/40 backdrop-blur-sm border border-red-500/20 rounded-2xl p-8 max-w-md w-full text-center space-y-6 shadow-2xl"
              style={contentAnimation}
            >
              <div className="text-red-400 text-lg font-medium">
                {error}
              </div>
              <Button
                variant="ghost"
                onClick={() => window.location.href = '/chat'}
                css="bg-[#FCA311] text-white font-semibold rounded-full px-6 py-3 hover:bg-[#E59100] transition-colors"
              >
                Back to Chat
              </Button>
            </animated.div>
          ) : success ? (
            <animated.div 
              className="bg-black/40 backdrop-blur-sm border border-green-500/20 rounded-2xl p-8 max-w-md w-full text-center space-y-6 shadow-2xl"
              style={contentAnimation}
            >
              <div className="text-[#FCA311] text-lg font-medium">
                Successfully connected to {coachDetails?.name}!
              </div>
              <div className="text-white/70">
                Redirecting to chat...
              </div>
            </animated.div>
          ) : coachDetails ? (
            <>
              {/* Logo and Headline Section */}
              <animated.div 
                className="flex flex-col items-center mb-4 sm:mb-6"
                style={contentAnimation}
              >
                {/* Logo and Awaken text as a centered unit */}
                <div className="flex items-center gap-2 mb-3">
                  <Logo size={32} />
                  <span className="text-[#FCA311] text-2xl font-medium">awaken</span>
                </div>
                <h1 className="text-white text-base sm:text-lg font-medium px-4 text-center">
                  awaken your infinite nature with <br />AI spiritual masters
                </h1>
              </animated.div>
              
              {/* Main Content Panel */}
              <animated.div 
                className="bg-black/40 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-6 max-w-md w-full text-center space-y-8 shadow-2xl"
                style={contentAnimation}
              >
              {/* Coach Portrait */}
              <div className="flex justify-center">
                <img
                  src={getCoachPortraitUrl(coachDetails.name)}
                  alt={`${coachDetails.name} Portrait`}
                  className="w-32 h-32 object-contain rounded-full border-2 border-orange-500/30"
                />
              </div>

              {/* Coach Name */}
              <div className="text-[#FCA311] text-3xl font-medium">
                {coachDetails.name}
              </div>

              {/* Coach Type */}
              {coachDetails.type && (
                <div className="uppercase tracking-wider font-light text-orange-300 text-sm whitespace-pre-wrap">
                  {coachDetails.type}
                </div>
              )}

              {/* Line separator */}
              <div className="w-16 h-0.5 bg-orange-500/30 mx-auto"></div>

              {/* Coach Description */}
              <div className="font-light leading-relaxed text-white text-base whitespace-pre-wrap text-left">
                {coachDetails.description || "No description available."}
              </div>

              {/* Specialties */}
              {coachDetails.metadata?.specialties && (
                <div>
                  <div className="font-medium text-orange-200 text-sm mb-3">
                    SPECIALTIES
                  </div>
                  <div className="flex flex-wrap justify-center gap-2">
                    {coachDetails.metadata.specialties.map((specialty: string, index: number) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-orange-900/40 text-orange-200 rounded-full text-xs border border-orange-700/30"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Data-sharing toggle - only show for non-authenticated users */}
              {(!user || !user.channelId) && (
                <div className="my-8 flex items-center justify-between bg-black border border-[#FCA311] rounded-2xl px-6 py-4 text-white">
                  <span className="text-base text-left flex-1 pr-4">
                    Let the actual {getHumanCoachName(coachDetails.name)} see my chats and message me
                  </span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={dataSharingEnabled}
                      onChange={handleToggleDataSharing}
                      className="sr-only peer"
                    />
                    <div className="w-14 h-8 bg-gray-700 rounded-full peer peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-amber-400 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-1 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all dark:border-gray-600 peer-checked:bg-[#FCA311]"></div>
                  </label>
                </div>
              )}

              {/* Connect Section */}
              <div className="space-y-4">
                {isAlreadyConnected ? (
                  <div className="text-green-400 text-lg font-medium text-center">
                    You are already connected to {coachDetails.name}!
                  </div>
                ) : (
                  <Button
                    variant="ghost"
                    onClick={handleConnect}
                    disabled={connecting}
                    css="bg-[#FCA311] text-white font-semibold rounded-full px-6 sm:px-10 py-4 hover:bg-[#E59100] 
                         transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed
                         shadow-[0_0_20px_rgba(252,163,17,0.3)] hover:shadow-[0_0_25px_rgba(252,163,17,0.5)]
                         w-full flex items-center justify-center"
                  >
                    {connecting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Connecting...
                      </div>
                    ) : (
                      <span>Connect with {coachDetails.name} in Awaken</span>
                    )}
                  </Button>
                )}

                <Button
                  variant="ghost"
                  onClick={() => (window.location.href = "/chat")}
                  css="text-white/70 hover:text-white transition-colors underline"
                >
                  skip
                </Button>
              </div>

              {/* Error Display */}
              {error && (
                <div className="text-red-400 text-sm">
                  {error}
                </div>
              )}
            </animated.div>
            </>
          ) : null}
        </div>
      </div>
    </Layout>
  );
}; 