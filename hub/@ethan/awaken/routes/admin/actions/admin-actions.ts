"use server";

import { UI_DEFAULT_TYPES, ADMIN_VISIBLE_TYPES, TEXTUAL_MSG_TYPES } from "../../../lib/message-types.ts";
import { db } from "../../../lib/db.ts";
import Reframe from "@";
import CryptoJS from "npm:crypto-js";
import { SUPERUSER_EMAILS, getAnalysisPrompt } from "../../../lib/server-constants.ts";
import { getSession } from "../../../lib/get-session.ts";
import { sql } from "npm:kysely";
import { saveMessage, uploadAudio, generateTranscription } from "../../../actions/db/conversation-actions.ts";
import { getCoachPrompt } from "../../../action.ts";
import { getAuthenticatedUser } from "../../../lib/auth-helper.ts";
import { v4 as uuidv4 } from "npm:uuid";
import { Tags } from "../../../lib/utils/tag-utils.ts";

// Types for analysis prompt modes
type AnalysisPromptMode = 
  | 'kokoro_meta'        // Admin analysis - uses getAnalysisPrompt
  | 'coach_summary';     // Coach-specific summary - uses coachSummaryPrompt as system

interface AnalysisOptions {
  mode: 'all' | 'single';
  model: 'gemini-2.5-pro-preview-06-05' | 'openai-openrouter' | 'openai-api-4.5' | 'openai-api-o1-medium' | 'openai-api-o1-high';
  promptMode: AnalysisPromptMode;
  coachName?: string; // Required when promptMode is 'coach_summary'
}

/**
 * Default summary prompt for when no coach-specific prompt is found
 */
const getDefaultSummaryPrompt = (): string => {
  return `Based on the conversation between the client and the coach, please provide a concise summary that includes:

* **Topics Discussed:**
* **People Involved (Names & Relationships):**
* **Breakthroughs:**
* **Proposed/Committed Actions:**
* **Client's Current State:**`;
};

/**
 * Efficiently fetches the most recent X messages for each channel with proper limits
 * 
 * @param channelIds - Array of channel IDs to fetch messages for
 * @param limit - Maximum number of messages to fetch per channel
 * @returns Messages grouped by channel ID
 */
async function getRecentMessagesForChannels(channelIds: number[], limit: number = 10) {
  if (channelIds.length === 0) return {};
  
  try {
    console.log(`[ADMIN] Fetching ${limit} recent messages for ${channelIds.length} channels`);
    
    // Initialize storage for messages by channel
    const groupedMessages: Record<string, any[]> = {};
    let totalMessagesRetrieved = 0;
    
    // Fetch messages for each channel separately with proper limits
    // This is more efficient than fetching all and filtering in memory
    for (const channelId of channelIds) {
      const messages = await db
        .selectFrom("conversation")
        .selectAll()
        .where("channelId", "=", channelId)
        .where("messageType", "=", "message")
        .orderBy("date", "desc")
        .limit(limit)
        .execute();
      
      totalMessagesRetrieved += messages.length;
      
      if (messages.length > 0) {
        // Reverse to get chronological order (oldest first)
        groupedMessages[channelId.toString()] = messages.reverse();
      }
    }
    
    console.log(`[ADMIN] Retrieved ${totalMessagesRetrieved} total messages (max ${limit} per channel)`);
    
    return groupedMessages;
  } catch (error) {
    console.error("[ADMIN] Error fetching recent messages:", error);
    throw error;
  }
}

/**
 * Fetches the most recent X messages overall and groups them by channel
 * This provides a different view compared to fetching per-channel messages
 * 
 * @param totalMessages - Total number of most recent messages to fetch
 * @returns Messages grouped by channel ID
 */
async function getLatestMessagesAndGroupByChannel(totalMessages: number = 100) {
  try {
    console.log(`[ADMIN] Fetching ${totalMessages} most recent messages overall`);
    
    // Fetch the most recent messages across all channels
    const messages = await db
      .selectFrom("conversation")
      .selectAll()
      .where("messageType", "=", "message")
      .orderBy("date", "desc")
      .limit(totalMessages)
      .execute();
    
    console.log(`[ADMIN] Retrieved ${messages.length} total messages`);
    
    // Group messages by channel
    const groupedMessages: Record<string, any[]> = {};
    
    // Process in reverse to maintain chronological order within each channel
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const channelId = message.channelId.toString();
      
      if (!groupedMessages[channelId]) {
        groupedMessages[channelId] = [];
      }
      
      groupedMessages[channelId].push(message);
    }
    
    console.log(`[ADMIN] Grouped into ${Object.keys(groupedMessages).length} channels`);
    
    return groupedMessages;
  } catch (error) {
    console.error("[ADMIN] Error fetching latest messages:", error);
    throw error;
  }
}

/**
 * Fetches all conversations grouped by channel ID
 * Admin-only function to retrieve chat history for analysis
 * @param fetchMode - Method to use for fetching conversations ('byChannel' or 'recentOverall')
 * @param messagesLimit - Number of messages to fetch (per channel or total, depending on mode)
 */
export const getAllChannelConversations = async (
  fetchMode: 'byChannel' | 'recentOverall' = 'byChannel',
  messagesLimit: number = fetchMode === 'byChannel' ? 40 : 200
) => {
  // Verify the user is an admin
  const user = await getSession();
  if (!user || !SUPERUSER_EMAILS.includes(user.email)) {
    throw new Error("Unauthorized: Admin access required");
  }
  
  try {
    console.log(`[ADMIN] Fetching all channel conversations using mode: ${fetchMode}`);
    
    let groupedMessagesWithEncryption: Record<string, any[]> = {};
    
    if (fetchMode === 'byChannel') {
      // First, get a list of all unique channel IDs
      const channelResults = await db
        .selectFrom("user")
        .select(["channelId"])
        .distinct()
        .execute();
      
      const channelIds = channelResults.map(row => row.channelId);
      console.log(`[ADMIN] Found ${channelIds.length} unique channels`);
      
      // Use our optimized query to get messages for all channels at once
      groupedMessagesWithEncryption = await getRecentMessagesForChannels(channelIds, messagesLimit);
    } else {
      // Fetch the most recent messages overall and group by channel
      groupedMessagesWithEncryption = await getLatestMessagesAndGroupByChannel(messagesLimit);
    }
    
    // Get the encryption key
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    // Process all channels and decrypt messages
    const groupedConversations: Record<string, any[]> = {};
    let totalMessages = 0;
    
    for (const channelId in groupedMessagesWithEncryption) {
      const messages = groupedMessagesWithEncryption[channelId];
      const decryptedMessages: any[] = [];
      
      // Decrypt messages for this channel
      for (const message of messages) {
        try {
          // Using the same decryption approach as in conversation-actions.ts
          let decryptedContent;
          try {
            // First try to decrypt the message
            decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
            
            // If decrypted content is empty but the original content isn't,
            // it might be an unencrypted legacy message
            if (!decryptedContent && message.content) {
              console.log(`[ADMIN] Message ${message.id} appears to be unencrypted, using original content`);
              decryptedContent = message.content;
            }
          } catch (decryptError) {
            // Skip messages that can't be decrypted and log error
            console.error(`[ADMIN] Failed to decrypt message ${message.id}:`, (decryptError as Error).message);
            continue;
          }
          
          // Skip messages with empty content after decryption attempts
          if (!decryptedContent) {
            console.log(`[ADMIN] Skipping message ${message.id} with empty content`);
            continue;
          }
          
          decryptedMessages.push({
            ...message,
            content: decryptedContent
          });
        } catch (error) {
          console.error(`[ADMIN] Error processing message ${message.id}:`, (error as Error).message);
        }
      }
      
      // Only add channels that have messages after decryption
      if (decryptedMessages.length > 0) {
        groupedConversations[channelId] = decryptedMessages;
        totalMessages += decryptedMessages.length;
      }
    }
    
    // Count channels and total messages
    const channelCount = Object.keys(groupedConversations).length;
    
    console.log(`[ADMIN] Successfully processed ${totalMessages} messages from ${channelCount} channels`);
    
    return { 
      conversations: groupedConversations,
      stats: {
        channelCount,
        totalMessages,
        fetchMode
      }
    };
  } catch (error) {
    console.error("[ADMIN] Error fetching all channel conversations:", error);
    throw error;
  }
};

/**
 * Analyzes conversations using AI
 * Takes in conversations (grouped by channel) and a query/prompt
 */
export const analyzeConversations = async (
  conversations: Record<string, any[]>,
  query: string,
  options: AnalysisOptions
) => {
  // Verify the user is an admin
  const user = await getSession();
  if (!user || !SUPERUSER_EMAILS.includes(user.email)) {
    throw new Error("Unauthorized: Admin access required");
  }
  
  try {
    console.log("[ADMIN] Analyzing conversations with query:", query);
    console.log("[ADMIN] Analysis mode:", options.mode);
    console.log("[ADMIN] Using model:", options.model);
    console.log("[ADMIN] Prompt mode:", options.promptMode);
    
    // Count total messages and channels
    const channelCount = Object.keys(conversations).length;
    let totalMessages = 0;
    
    Object.values(conversations).forEach(channelMessages => {
      totalMessages += channelMessages.length;
    });
    
    console.log(`[ADMIN] Analyzing ${totalMessages} messages from ${channelCount} channels`);
    
    // For single user analysis, we can include more messages
    // For coach summaries, use all messages since we already filtered to 2 weeks
    const messagesPerChannel = options.promptMode === 'coach_summary' 
      ? Number.MAX_SAFE_INTEGER  // Use all messages for summaries
      : (options.mode === 'single' ? 100 : 40); // Keep limits for analysis
    
    console.log(`[ADMIN] Message limit per channel: ${messagesPerChannel === Number.MAX_SAFE_INTEGER ? 'unlimited (coach summary)' : messagesPerChannel}`);
    
    // Prepare conversations in a format suitable for AI
    const conversationSummaries = Object.entries(conversations).map(([channelId, messages]) => {
      // Limit messages per channel for analysis, but use all for summaries
      const limitedMessages = messages.slice(-messagesPerChannel);
      
      console.log(`[ADMIN] Channel ${channelId}: Using ${limitedMessages.length}/${messages.length} messages`);
      
      return `
CHANNEL_ID: ${channelId}
MESSAGE_COUNT: ${messages.length}
MESSAGES:
${limitedMessages.map(msg => 
  `[${new Date(msg.date).toISOString()}] ${msg.sender}: ${msg.content}`
).join('\n')}
      `;
    }).join('\n\n====================\n\n');
    
    // Resolve system instruction and user prompt based on prompt mode
    let systemInstruction: string;
    let userPrompt: string;

    switch (options.promptMode) {
      case 'coach_summary':
        if (!options.coachName) {
          throw new Error("coachName is required when promptMode is 'coach_summary'");
        }
        
        console.log(`[ADMIN] Using coach-specific summary prompt for coach: ${options.coachName}`);
        const { SystemPrompt: coachPrompt } = await getCoachPrompt(options.coachName, "coachSummaryPrompt");
        
        // Use coach-specific prompt or fallback to default summary prompt
        systemInstruction = coachPrompt || getDefaultSummaryPrompt();
        userPrompt = conversationSummaries; // The formatted conversation data
        
        console.log(`[ADMIN] Coach prompt found: ${!!coachPrompt}`);
        break;
        
      case 'kokoro_meta':
      default:
        console.log("[ADMIN] Using Kokoro Meta analysis prompt");
        systemInstruction = getAnalysisPrompt(options.mode, totalMessages, conversationSummaries, channelCount);
        userPrompt = query; // The user's analysis question
        break;
    }
      
    // Call the appropriate model API based on the selected model
    let result;
    if (options.model === 'openai-openrouter') {
      // Use OpenRouter with OpenAI model
      result = await callOpenRouterAPI("openai/gpt-4.5-preview", systemInstruction, userPrompt);
    } else if (options.model === 'openai-api-4.5') {
      // Use OpenAI API directly with GPT-4.5
      result = await callOpenAIDirectAPI("gpt-4.5-preview-2025-02-27", systemInstruction, userPrompt);
    } else if (options.model === 'openai-api-o1-medium') {
      // Use OpenAI API directly with o1 model (medium reasoning)
      result = await callOpenAIDirectAPI("o1-2024-12-17", systemInstruction, userPrompt, "medium");
    } else if (options.model === 'openai-api-o1-high') {
      // Use OpenAI API directly with o1 model (high reasoning)
      result = await callOpenAIDirectAPI("o1-2024-12-17", systemInstruction, userPrompt, "high");
    } else if (options.model === 'gemini-2.5-pro-preview-06-05') {
      // Use OpenRouter with Gemini 2.5 Pro model
      result = await callOpenRouterAPI("google/gemini-2.5-pro-preview-06-05", systemInstruction, userPrompt);
    } else {
      // Fallback case: Log an error or default to a known model
      // For safety, let's default to the new Gemini model if the type somehow allows an unexpected value
      console.warn(`[ADMIN] Unexpected model value: ${options.model}. Defaulting to gemini-2.5-pro-preview-06-05.`);
      result = await callOpenRouterAPI("google/gemini-2.5-pro-preview-06-05", systemInstruction, userPrompt);
    }
    
    return result;
  } catch (error) {
    console.error("[ADMIN] Error analyzing conversations:", error);
    throw error;
  }
};

/**
 * Call OpenRouter API with specified model for conversation analysis
 */
async function callOpenRouterAPI(modelId: string, systemInstruction: string, userPrompt: string) {
  try {
    console.log(`[ADMIN] Calling OpenRouter API with model: ${modelId}`);
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question with conversation history");
      
      // Log the full prompt for debugging
      console.log("[ADMIN] Full follow-up prompt:", userPrompt);
    }
    
    // OpenRouter API configuration
    const API_KEY = "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";
    const openRouterUrl = "https://openrouter.ai/api/v1/chat/completions";
    
    // For follow-up questions, we need to properly extract the actual question from the context
    let messages: Array<{role: string; content: string}> = [];
    
    if (isFollowUp) {
      try {
        // Extract the actual question (first line before the context)
        const splitPrompt = userPrompt.split('\n\nThis is a follow-up question');
        const actualQuestion = splitPrompt[0].trim();
        
        // Parse out the conversation history
        const historyPart = userPrompt.split("For context, here's the history:")[1];
        if (!historyPart) {
          console.error("[ADMIN] Could not extract history from follow-up prompt");
          throw new Error("Failed to parse conversation history");
        }
        
        const conversationParts = historyPart.trim().split('\n\n').filter(Boolean);
        console.log(`[ADMIN] Found ${conversationParts.length} conversation parts`);
        
        // Build the messages array with the conversation history
        messages = [
          {
            role: "system",
            content: systemInstruction
          }
        ];
        
        // Add each Q&A pair from the history
        for (const conv of conversationParts) {
          const parts = conv.split('\nResponse: ');
          if (parts.length === 2) {
            const question = parts[0].replace('Question: ', '').trim();
            const answer = parts[1].trim();
            
            if (question && answer) {
              messages.push({ role: "user", content: question });
              messages.push({ role: "assistant", content: answer });
              console.log(`[ADMIN] Added Q&A pair - Q: ${question.substring(0, 30)}... A: ${answer.substring(0, 30)}...`);
            }
          }
        }
        
        // Add the current question
        messages.push({ role: "user", content: actualQuestion });
        console.log(`[ADMIN] Added current question: ${actualQuestion}`);
        console.log(`[ADMIN] Total messages in conversation: ${messages.length}`);
      } catch (parseError) {
        console.error("[ADMIN] Error parsing follow-up conversation:", parseError);
        // Fallback to basic prompt if parsing fails
        messages = [
          { role: "system", content: systemInstruction },
          { role: "user", content: userPrompt.split('\n')[0] } // Just use the first line
        ];
      }
    } else {
      // Regular (non-follow-up) question
      messages = [
        {
          role: "system",
          content: systemInstruction
        },
        {
          role: "user",
          content: userPrompt
        }
      ];
    }
    
    // Prepare the request payload for OpenRouter
    const payload = {
      model: modelId,
      messages: messages,
      temperature: 0.2,
      max_tokens: 4000
    };
    
    console.log(`[ADMIN] Sending ${messages.length} messages to OpenRouter`);
    
    // Make the API request to OpenRouter
    const startTime = Date.now();
    const response = await fetch(openRouterUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${API_KEY}`
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }
    
    const apiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (apiResponse.choices && apiResponse.choices.length > 0) {
      analysis = apiResponse.choices[0].message.content;
    }
    
    if (!analysis) {
      throw new Error(`No analysis was generated by ${modelId}`);
    }
    
    // Calculate token usage - OpenRouter provides usage information
    const tokenInfo = {
      inputTokens: apiResponse.usage?.prompt_tokens || 0,
      outputTokens: apiResponse.usage?.completion_tokens || 0,
      totalTokens: apiResponse.usage?.total_tokens || 0,
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] ${modelId} analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error(`[ADMIN] Error calling OpenRouter API:`, error);
    throw error;
  }
}

/**
 * Call OpenAI API directly with specified model for conversation analysis
 */
async function callOpenAIDirectAPI(
  modelId: string, 
  systemInstruction: string, 
  userPrompt: string,
  reasoningEffort: "low" | "medium" | "high" | null = null
) {
  try {
    console.log(`[ADMIN] Calling OpenAI API directly with model: ${modelId}`);
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question with conversation history");
      console.log("[ADMIN] Full follow-up prompt:", userPrompt);
    }
    
    // OpenAI API configuration
    const API_KEY = "********************************************************************************************************************************************************************";
    const openAIUrl = "https://api.openai.com/v1/chat/completions";
    
    // For follow-up questions, we need to properly extract the actual question from the context
    let messages: Array<{role: string; content: string}> = [];
    
    if (isFollowUp) {
      try {
        // Extract the actual question (first line before the context)
        const splitPrompt = userPrompt.split('\n\nThis is a follow-up question');
        const actualQuestion = splitPrompt[0].trim();
        
        // Parse out the conversation history
        const historyPart = userPrompt.split("For context, here's the history:")[1];
        if (!historyPart) {
          console.error("[ADMIN] Could not extract history from follow-up prompt");
          throw new Error("Failed to parse conversation history");
        }
        
        const conversationParts = historyPart.trim().split('\n\n').filter(Boolean);
        console.log(`[ADMIN] Found ${conversationParts.length} conversation parts`);
        
        // Build the messages array with the conversation history
        messages = [
          {
            role: "system",
            content: systemInstruction
          }
        ];
        
        // Add each Q&A pair from the history
        for (const conv of conversationParts) {
          const parts = conv.split('\nResponse: ');
          if (parts.length === 2) {
            const question = parts[0].replace('Question: ', '').trim();
            const answer = parts[1].trim();
            
            if (question && answer) {
              messages.push({ role: "user", content: question });
              messages.push({ role: "assistant", content: answer });
              console.log(`[ADMIN] Added Q&A pair - Q: ${question.substring(0, 30)}... A: ${answer.substring(0, 30)}...`);
            }
          }
        }
        
        // Add the current question
        messages.push({ role: "user", content: actualQuestion });
        console.log(`[ADMIN] Added current question: ${actualQuestion}`);
        console.log(`[ADMIN] Total messages in conversation: ${messages.length}`);
      } catch (parseError) {
        console.error("[ADMIN] Error parsing follow-up conversation:", parseError);
        // Fallback to basic prompt if parsing fails
        messages = [
          { role: "system", content: systemInstruction },
          { role: "user", content: userPrompt.split('\n')[0] } // Just use the first line
        ];
      }
    } else {
      // Regular (non-follow-up) question
      messages = [
        {
          role: "system",
          content: systemInstruction
        },
        {
          role: "user",
          content: userPrompt
        }
      ];
    }
    
    // Prepare the request payload for OpenAI
    // o1 model uses different parameters
    const isO1Model = modelId.startsWith('o1');
    
    // Base payload with common parameters
    const payload: any = {
      model: modelId,
      messages: messages
    };
    
    // Add model-specific parameters
    if (isO1Model) {
      // o1 models use max_completion_tokens instead of max_tokens
      // and don't support temperature
      payload.max_completion_tokens = 4000;
      
      // Add reasoning_effort if specified (only for o1 models)
      if (reasoningEffort) {
        payload.reasoning_effort = reasoningEffort;
        console.log(`[ADMIN] Using reasoning_effort: ${reasoningEffort} for o1 model`);
      }
    } else {
      // Non-o1 models use standard parameters
      payload.max_tokens = 4000;
      payload.temperature = 0.2;
    }
    
    console.log(`[ADMIN] Sending ${messages.length} messages to OpenAI API`);
    
    // Make the API request to OpenAI
    const startTime = Date.now();
    const response = await fetch(openAIUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${API_KEY}`
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} ${errorText}`);
    }
    
    const apiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (apiResponse.choices && apiResponse.choices.length > 0) {
      analysis = apiResponse.choices[0].message.content;
    }
    
    if (!analysis) {
      throw new Error(`No analysis was generated by ${modelId}`);
    }
    
    // Calculate token usage - OpenAI provides usage information
    const tokenInfo = {
      inputTokens: apiResponse.usage?.prompt_tokens || 0,
      outputTokens: apiResponse.usage?.completion_tokens || 0,
      totalTokens: apiResponse.usage?.total_tokens || 0,
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] ${modelId} analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error(`[ADMIN] Error calling OpenAI API:`, error);
    throw error;
  }
}

/**
 * Legacy direct Gemini API call - kept for reference or future use
 */
async function callGeminiAPI(systemInstruction: string, userPrompt: string) {
  try {
    console.log("[ADMIN] Calling Gemini API directly (legacy method)");
    
    // Check if this is a follow-up question
    const isFollowUp = userPrompt.includes("This is a follow-up question to our previous conversation");
    
    if (isFollowUp) {
      console.log("[ADMIN] Processing as follow-up question");
    }
    
    // Gemini API configuration
    const API_KEY = Reframe.env.GEMINI_API_KEY || "AIzaSyDIPxlOphEVJNdGdeB0Oob-1WQNDOTssmI"; // Fallback to public key if env not set
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${API_KEY}`;
    
    // Prepare the request payload for Gemini
    const payload = {
      contents: [
        {
          role: "user",
          parts: [
            { text: userPrompt }
          ]
        }
      ],
      systemInstruction: {
        parts: [
          { text: systemInstruction }
        ]
      },
      generationConfig: {
        temperature: 0.2,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 4000,
        candidateCount: 1,
        stopSequences: []
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_NONE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_NONE"
        }
      ],
      tools: []
    };
    
    // Make the API request to Gemini
    const startTime = Date.now();
    const response = await fetch(geminiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });
    
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000; // Convert to seconds
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gemini API error: ${response.status} ${errorText}`);
    }
    
    const geminiResponse = await response.json();
    
    // Extract the analysis from the response
    let analysis = "";
    if (geminiResponse.candidates && geminiResponse.candidates.length > 0) {
      if (geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts.length > 0) {
        analysis = geminiResponse.candidates[0].content.parts[0].text;
      }
    }
    
    if (!analysis) {
      throw new Error("No analysis was generated by Gemini");
    }
    
    // Calculate token usage
    const tokenInfo = {
      inputTokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
      outputTokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: (geminiResponse.usageMetadata?.promptTokenCount || 0) + (geminiResponse.usageMetadata?.candidatesTokenCount || 0),
      responseTime: responseTime.toFixed(2)
    };
    
    console.log(`[ADMIN] Gemini analysis completed successfully. Token usage:`, tokenInfo);
    
    return { 
      analysis, 
      tokenInfo
    };
  } catch (error) {
    console.error("[ADMIN] Error calling Gemini API:", error);
    throw error;
  }
}

/**
 * Get all users who have sent messages to a specific coach
 * Returns user list with their last message preview
 */
export const getCoachUsersWithLastMessage = async (coachEmail: string) => {
  try {
    console.log(`[COACH_USERS] Fetching users for coach email: ${coachEmail}`);
    
    // First, get the coach details to find their name
    const coach = await db
      .selectFrom("coaches")
      .select(["name"])
      .where("email", "=", coachEmail)
      .executeTakeFirst();

    if (!coach) {
      throw new Error("Coach not found");
    }

    const coachName = coach.name;
    console.log(`[COACH_USERS] Found coach name: ${coachName}`);

    // Step 1: Get all channel IDs that have data sharing enabled for this coach
    const enabledChannels = await db
      .selectFrom("userCoachAttributes")
      .select(["channelId"])
      .where("coachName", "=", coachName)
      .where("dataSharingEnabled", "=", 1)
      .execute();

    const channelIds = enabledChannels.map(ch => ch.channelId);
    
    if (channelIds.length === 0) {
      console.log(`[COACH_USERS] No channels with data sharing enabled for coach ${coachName}`);
      return { coachName, users: [], totalUsers: 0 };
    }

    console.log(`[COACH_USERS] Found ${channelIds.length} channels with data sharing enabled`);

    // Step 2: Get the latest message for each channel (using a more efficient subquery approach)
    const latestMessages = await db
      .selectFrom("conversation as c1")
      .select([
        "c1.channelId",
        "c1.content",
        "c1.date", 
        "c1.sender",
        "c1.messageType"
      ])
      .where("c1.channelId", "in", channelIds)
      .where("c1.coachName", "=", coachName)
      .where("c1.messageType", "in", ["message", "summary", "coach_message"])
      .where("c1.date", "=", (eb) =>
        eb.selectFrom("conversation as c2")
          .select([eb.fn.max("c2.date").as("maxDate")])
          .where("c2.channelId", "=", eb.ref("c1.channelId"))
          .where("c2.coachName", "=", coachName)
          .where("c2.messageType", "in", ["message", "summary", "coach_message"])
      )
      .execute();

    console.log(`[COACH_USERS] Found ${latestMessages.length} latest messages`);

    // Step 3: Get user details for these channels
    const users = await db
      .selectFrom("user")
      .select([
        "channelId",
        "name as userName",
        "email as userEmail",
        "image as userImage", 
        "currentPlan as userCurrentPlan"
      ])
      .where("channelId", "in", channelIds)
      .execute();

    console.log(`[COACH_USERS] Found ${users.length} user records`);

    // Step 4: Get unseen message flags in batch
    const unseenFlags = await db
      .selectFrom("conversation")
      .select([
        "channelId",
        "messageType",
        sql<number>`CASE WHEN seen_by_coach = 0 OR seen_by_coach IS NULL THEN 1 ELSE 0 END`.as("hasUnseen")
      ])
      .where("channelId", "in", channelIds)
      .where("coachName", "=", coachName)
      .where("messageType", "in", ["message", "call", "summary", "coach_message"])
      .where("sender", "=", "user")
      .where(eb => eb.or([
        eb("seenByCoach", "=", 0),
        eb("seenByCoach", "is", null)
      ]))
      .execute();

    console.log(`[COACH_USERS] Found ${unseenFlags.length} unseen messages`);

    // Process data in memory (much faster than complex joins)
    const latestMessageMap = new Map(latestMessages.map(msg => [msg.channelId, msg]));
    const userMap = new Map(users.map(user => [user.channelId, user]));
    
    // Process unseen flags by channel
    const unseenByChannel: Record<number, { regular: boolean; thread: boolean }> = {};
    for (const flag of unseenFlags) {
      if (!unseenByChannel[flag.channelId]) {
        unseenByChannel[flag.channelId] = { regular: false, thread: false };
      }
      
      if (flag.messageType === "coach_message") {
        unseenByChannel[flag.channelId].thread = Boolean(flag.hasUnseen);
      } else if (["message", "call", "summary"].includes(flag.messageType)) {
        unseenByChannel[flag.channelId].regular = Boolean(flag.hasUnseen);
      }
    }

    // Combine all data
    const combinedUsers = channelIds
      .map(channelId => {
        const user = userMap.get(channelId);
        const lastMessage = latestMessageMap.get(channelId);
        
        if (!user || !lastMessage) {
          return null; // Skip if missing data
        }

        return {
          channelId,
          userName: user.userName || `User ${channelId}`,
          userEmail: user.userEmail,
          userImage: user.userImage,
          userCurrentPlan: user.userCurrentPlan,
          lastMessageContent: lastMessage.content,
          lastMessageDate: lastMessage.date,
          lastMessageSender: lastMessage.sender,
          lastMessageType: lastMessage.messageType
        };
      })
      .filter(user => user !== null);

    console.log(`[COACH_USERS] Combined ${combinedUsers.length} users with all data`);

    // Decrypt the last messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }

    const decryptedUsers = combinedUsers.map(user => {
      try {
        let decryptedContent = "";
        if (user.lastMessageContent) {
          try {
            decryptedContent = CryptoJS.AES.decrypt(user.lastMessageContent, secretKey).toString(CryptoJS.enc.Utf8);

            if(user.channelId === 548445911){
              console.log("DECRYPTED CONTENT", decryptedContent);
            }
            // If decryption results in empty string, might be unencrypted legacy message
            if (!decryptedContent && user.lastMessageContent) {
              decryptedContent = user.lastMessageContent;
            }
          } catch (decryptError) {
            console.warn(`[COACH_USERS] Failed to decrypt message for channel ${user.channelId}`);
            decryptedContent = "[Unable to decrypt message]";
            console.error(`[COACH_USERS] Failed to decrypt message for channel ${user.channelId}:`, decryptError);
          }
        }

        const unseenData = unseenByChannel[user.channelId] || { regular: false, thread: false };

        return {
          channelId: user.channelId,
          userName: user.userName || `User ${user.channelId}`,
          userCurrentPlan: user.userCurrentPlan,
          userEmail: user.userEmail,
          userImage: user.userImage,

          hasUnseen: {
            regularMessage: unseenData.regular,
            userThreadMessage: unseenData.thread,
          },
          lastMessage: {
            content: decryptedContent,
            date: user.lastMessageDate,
            sender: user.lastMessageSender,
            type: user.lastMessageType
          }
        };
      } catch (error) {
        console.error(`[COACH_USERS] Error processing user ${user.channelId}:`, error);
        return null;
      }
    }).filter(user => user !== null);

    // Fetch tags for all users using the existing function
    const channelIdsForTags = decryptedUsers.map(user => user.channelId);
    const userTags = await getUsersTagsForCoach(channelIdsForTags, coachName);

    // Add tags to each user
    const usersWithTags = decryptedUsers.map(user => ({
      ...user,
      tags: userTags[user.channelId] || []
    }));

    return {
      coachName,
      users: usersWithTags,
      totalUsers: usersWithTags.length
    };
  } catch (error) {
    console.error("[COACH_USERS] Error fetching coach users:", error);
    throw error;
  }
};

/**
 * Get all messages between a specific user and coach
 */
export const getCoachUserConversation = async (
  channelId: string | number,
  coach: {
    name: string;
    email: string;
    description: string;
    metadata: string;
  }, 
  limit: number = 50,
  beforeDate?: string
) => {
  // Verify the user is authorized
  // const user = await getSession();
  // if (!user) {
  //   throw new Error("Unauthorized: Authentication required");
  // }

  // // Check if user is superuser or the coach themselves
  // const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
  // const isCoach = user.email === coach.email;
  
  // if (!isSuperUser && !isCoach) {
  //   throw new Error("Unauthorized: You can only view your own conversations");
  // }

  try {
    console.log(`[COACH_CONVERSATION] Fetchingggg conversation for coach ${coach.name} and user ${channelId}`);
    
    console.log("COACH EMAIL", coach.email);

    if (!coach) {
      throw new Error("Coach not found");
    }

    const coachName = coach.name;

    // Ensure the user has enabled data sharing with this coach
    const sharingPref = await db
      .selectFrom("userCoachAttributes")
      .select(["dataSharingEnabled"])
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .executeTakeFirst();

    if (!sharingPref || sharingPref.dataSharingEnabled !== 1) {
      console.log(`[COACH_CONVERSATION] Data sharing disabled for user ${channelId} and coach ${coachName}`);
      return [];
    }

    // Build the query
    let query = db
      .selectFrom("conversation")
      .selectAll()
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("messageType", "in", [...ADMIN_VISIBLE_TYPES])
      .orderBy("date", "desc")
      .limit(limit);

    // Add date filter if provided
    if (beforeDate) {
      query = query.where("date", "<", beforeDate);
    }

    const messages = await query.execute();
    console.log(`[COACH_CONVERSATION] Retrieved ${messages.length} messages`);

    // Decrypt messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }

    const decryptedMessages = messages.map(message => {
      try {
        let decryptedContent = "";
        if (message.content) {
          try {
            decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
            
            if (!decryptedContent && message.content) {
              decryptedContent = message.content;
            }
          } catch (decryptError) {
            console.warn(`[COACH_CONVERSATION] Failed to decrypt message ${message.id}`);
            decryptedContent = "[Unable to decrypt message]";
          }
        }

        return {
          id: message.id,
          channelId: message.channelId,
          sender: message.sender,
          content: decryptedContent,
          date: message.date,
          status: message.status,
          messageType: message.messageType,
          audio: message.audio,
          coachName: message.coachName,
          seenByCoach: message.seenByCoach
        };
      } catch (error) {
        console.error(`[COACH_CONVERSATION] Error processing message ${message.id}:`, error.message);
        return null;
      }
    }).filter(message => message !== null);

    // Return in chronological order (oldest first)
    return decryptedMessages.reverse();
  } catch (error) {
    console.error("[COACH_CONVERSATION] Error fetching conversation:", error);
    throw error;
  }
};

/**
 * Get coach details by email
 */
export const getCoachByEmail = async (email: string) => {
  try {
    const coach = await db
      .selectFrom("coaches")
      .select(["id", "name", "email", "description", "type"])
      .where("email", "=", email)
      .executeTakeFirst();

    return coach || null;
  } catch (error) {
    console.error("[GET_COACH_BY_EMAIL] Error:", error);
    return null;
  }
};

/**
 * Get messages for a coach since a specific timestamp (for incremental sync)
 * This is the coach-side equivalent of getMessagesSinceTimestamp
 */
export const getCoachMessagesSinceTimestamp = async (
  coachEmail: string,
  since: string,
  limit: number = 20,
  messageTypes: string[] = [...ADMIN_VISIBLE_TYPES]
) => {
  try {
    // Verify the user is a coach
    const user = await getSession();
    if (!user || user.email !== coachEmail) {
      throw new Error("Unauthorized: Coach access required");
    }

    // Get the coach details to find their name
    const coach = await db
      .selectFrom("coaches")
      .select(["name"])
      .where("email", "=", coachEmail)
      .executeTakeFirst();

    if (!coach) {
      throw new Error("Coach not found");
    }

    const coachName = coach.name;

    // Fetch messages since the timestamp for this coach
    const messages = await db
      .selectFrom("conversation")
      .selectAll()
      .where("coachName", "=", coachName)
      .where("messageType", "in", messageTypes)
      .where("date", ">", since)
      .orderBy("date", "asc")
      .limit(limit)
      .execute();

    // Decrypt messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }

    const decryptedMessages = messages.map(message => {
      try {
        let decryptedContent = message.content;
        if (message.content && message.content.trim() !== "") {
          try {
            const bytes = CryptoJS.AES.decrypt(message.content, secretKey);
            const decrypted = bytes.toString(CryptoJS.enc.Utf8);
            if (decrypted) {
              decryptedContent = decrypted;
            }
          } catch (decryptError) {
            // Use original content if decryption fails
          }
        }

        return {
          Id: message.id,
          Date: message.date,
          Sender: message.sender,
          Type: message.messageType,
          Content: decryptedContent,
          CoachName: message.coachName,
          Audio: message.audio,
          Status: message.status,
          ChannelId: message.channelId,
          SeenByCoach: message.seenByCoach,
          SeenByUser: message.seenByUser
        };
      } catch (error) {
        console.error(`Error processing message ${message.id}:`, error);
        return null;
      }
    }).filter(message => message !== null);

    return decryptedMessages;
  } catch (error) {
    console.error("Error fetching messages since timestamp:", error);
    throw error;
  }
};

/**
 * Generate a summary for a single user's conversation (last 2 weeks)
 * Returns the AI-generated summary and token usage info
 */
export const summarizeUserConversation = async (
  channelId: string | number,
  coachName: string,
  model:
    | 'gemini-2.5-pro-preview-06-05'
    | 'openai-openrouter'
    | 'openai-api-4.5'
    | 'openai-api-o1-medium'
    | 'openai-api-o1-high' = 'gemini-2.5-pro-preview-06-05',
) => {
  try {
    // First, find the most recent message from this user to this coach
    const mostRecentMessage = await db
      .selectFrom('conversation')
      .select(['date'])
      .where('channelId', '=', Number(channelId))
      .where('coachName', '=', coachName)
      .where('messageType', 'in', TEXTUAL_MSG_TYPES)
      .orderBy('date', 'desc')
      .limit(1)
      .executeTakeFirst();

    if (!mostRecentMessage) {
      throw new Error('No messages found for this user and coach');
    }

    // Calculate 2 weeks back from the most recent message
    const mostRecentMessageDate = new Date(mostRecentMessage.date);
    const twoWeeksBeforeLatest = new Date(mostRecentMessageDate.getTime() - 14 * 24 * 60 * 60 * 1000);

    // Fetch messages within the calculated 2-week range
    const recentMessages = await db
      .selectFrom('conversation')
      .selectAll()
      .where('channelId', '=', Number(channelId))
      .where('coachName', '=', coachName)
      .where('messageType', 'in', TEXTUAL_MSG_TYPES)
      .where('date', '>=', twoWeeksBeforeLatest.toISOString())
      .where('date', '<=', mostRecentMessageDate.toISOString())
      .orderBy('date', 'asc')
      .execute();

    console.log(
      `[ADMIN] Fetched ${recentMessages.length} messages for summary (last 2 weeks) in channel ${channelId}`,
    );

    // Decrypt message contents
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error('Encryption key not found');
    }

    const decryptedMessages = recentMessages.map((msg) => {
      let decryptedContent = '';
      try {
        decryptedContent = CryptoJS.AES.decrypt(msg.content, secretKey).toString(CryptoJS.enc.Utf8);
        if (!decryptedContent && msg.content) {
          // Legacy unencrypted message fallback
          decryptedContent = msg.content;
        }
      } catch (err) {
        console.warn(`[ADMIN] Failed to decrypt message ${msg.id}`);
        decryptedContent = '[Unable to decrypt message]';
      }

      return { ...msg, content: decryptedContent };
    });

    // Prepare conversations object expected by analyzeConversations
    const conversations: Record<string, any[]> = {
      [String(channelId)]: decryptedMessages,
    };

    // Use analyzeConversations with coach_summary prompt mode
    const result = await analyzeConversations(conversations, "", {
      mode: 'single',
      model: model,
      promptMode: 'coach_summary',
      coachName: coachName
    });

    // Format the date range and prepend to the summary content
    const startDateFormatted = twoWeeksBeforeLatest.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    const endDateFormatted = mostRecentMessageDate.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    
    const formattedSummary = `From ${startDateFormatted} to ${endDateFormatted}\n\n${result.analysis}`;

    // NEW: Save the generated summary as a conversation message of type "summary"
    try {
      await saveMessage(
        channelId,
        "assistant",
        formattedSummary,
        undefined,
        "Default",
        "summary",
        coachName,
      );
    } catch (saveErr) {
      console.error('[ADMIN] Error saving summary message:', saveErr);
    }

    return {
      summary: formattedSummary,
      tokenInfo: result.tokenInfo,
      dateRange: {
        startDate: twoWeeksBeforeLatest.toISOString(),
        endDate: mostRecentMessageDate.toISOString(),
      },
    };
  } catch (error) {
    console.error('[ADMIN] Error generating user conversation summary:', error);
    throw error;
  }
};

/**
 * Send a direct message from the coach to a user. The message will be stored
 * in the conversation table with the message_type set to "coach_message".
 * The caller must be either the coach themselves (matching email) or a
 * super-user.
 */
/**
 * Persist a coach → user voice note.
 * Returns the row created by saveMessage so the UI can merge it.
 */
export async function sendCoachVoiceMessage(
  channelId: string | number,
  coach: { name: string; email?: string },
  audioBlob: Blob,
) {
  const messageId = uuidv4();                      // deterministic id for file + optimistic UI
  const audioUrl  = await uploadAudio(audioBlob, String(channelId), messageId);

  let transcript = "";
  try {
    transcript = await generateTranscription(audioBlob, "deepgram");
    // Deepgram returns an object with { text: "..." }
    if (transcript && typeof transcript === 'object' && 'text' in transcript) {
      transcript = transcript.text;
    }
  } catch (err) {
    console.warn("[ADMIN] transcription failed → continue:", err);
    // Use a placeholder if transcription fails
    transcript = "[Voice message]";
  }

  // Store as "coach_message" so it appears in the coach thread on the user side
  return await saveMessage(
    channelId,
    "coach",
    transcript || "[Voice message]",  // Ensure we always have content
    undefined,                 // date = now
    "Default",
    "coach_message",
    coach.name,
    audioUrl,
  );
}

export const sendCoachMessage = async (
  channelId: string | number,
  coach: { name: string; email?: string },
  content: string
) => {
  // Auth:
  const sessionUser = await getAuthenticatedUser();
  if (!sessionUser) {
    throw new Error("Unauthorized: authentication required");
  }

  const isSuperUser = SUPERUSER_EMAILS.includes(sessionUser.email);
  const isCoach = sessionUser.email === coach.email;
  // if (!isSuperUser && !isCoach) {
  //   throw new Error("Unauthorized: only the coach or a super-user can send direct messages");
  // }

  // Guard empty content
  const trimmed = content.trim();
  if (!trimmed) {
    throw new Error("Cannot send an empty message");
  }

  // Persist the message. We deliberately set sender to "coach" so that the
  // client can easily distinguish it from assistant responses.
  const saved = await saveMessage(
    channelId,
    "coach",
    trimmed,
    new Date().toISOString(),
    "Default",
    "coach_message",
    coach.name,
  );

  // Send push notification to user
  try {
    const { sendCoachMessageNotification } = await import("../../../actions/push-notifications/push-notification-service.ts");
    await sendCoachMessageNotification({
      channelId,
      coachName: coach.name,
      messageContent: trimmed,
      messageId: saved?.id,
    });
    console.log(`[COACH_MESSAGE] Push notification sent for message from ${coach.name} to channel ${channelId}`);
  } catch (error) {
    console.error(`[COACH_MESSAGE] Failed to send push notification:`, error);
    // Don't fail the message sending if push notification fails
  }

  // Return a lightweight object that the client can append to its local
  // state without an additional round-trip for decryption.
  return {
    id: saved?.id || "temp-" + Date.now().toString(),
    channelId: Number(channelId),
    sender: "coach",
    content: trimmed,
    date: new Date().toISOString(),
    status: "Default",
    messageType: "coach_message",
    audio: null,
    coachName: coach.name,
  };
};

// Mark REGULAR conversation messages (message and summary) as seen by coach
export const markConversationMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seen_by_coach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where("messageType", "in", ["message", "call", "summary"])
      .where((eb) => eb.or([
        eb("seen_by_coach", "=", 0),
        eb("seen_by_coach", "is", null),
      ]))
      .execute();
    console.log(`[ADMIN] Marked CONVERSATION messages seen for channel ${channelId}`);
  } catch (error) {
    console.error("[ADMIN] Error marking conversation messages seen:", error);
  }
};

// Mark THREAD (coach_message) messages as seen by coach
export const markThreadMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seenByCoach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where("messageType", "=", "coach_message")
      .where((eb) => eb.or([
        eb("seenByCoach", "=", 0),
        eb("seenByCoach", "is", null),
      ]))
      .execute();
    console.log(`[ADMIN] Marked THREAD messages seen for channel ${channelId}`);
  } catch (error) {
    console.error("[ADMIN] Error marking thread messages seen:", error);
  }
};

// Legacy (kept for compatibility): mark all messages as seen
export const markMessagesSeenByCoach = async (
  channelId: string | number,
  coachName: string
) => {
  try {
    await db
      .updateTable("conversation")
      .set({ seenByCoach: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where(eb => eb("seenByCoach", "=", 0).or("seenByCoach", "is", null))
      .execute();
    console.log(`[ADMIN] Marked messages as seen for channel ${channelId} and coach ${coachName}`);
  } catch (error) {
    console.error("[ADMIN] Error marking ALL messages as seen:", error);
  }
};

// User-Coach Tags Management Functions

/**
 * Get all tags for a specific user-coach pair
 */
export const getUserCoachTags = async (
  channelId: string | number,
  coachName: string
): Promise<string[]> => {
  try {
    const tags = await db
      .selectFrom("userCoachTags")
      .select("tag")
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .orderBy("tag", "asc")
      .execute();

    return tags.map(t => t.tag);
  } catch (error) {
    console.error("[ADMIN] Error fetching user coach tags:", error);
    return [];
  }
};

/**
 * Add a tag to a user-coach pair
 */
export const addUserCoachTag = async (
  channelId: string | number,
  coachName: string,
  tag: string
): Promise<boolean> => {
  try {
    const norm = Tags.normalize(tag);
    if (!norm) return false;
    await db
      .insertInto("userCoachTags")
      .values({
        channelId: Number(channelId),
        coachName,
        tag: norm
      })
      .onConflict((oc) => oc.doNothing()) // Ignore if tag already exists
      .execute();

    console.log(`[ADMIN] Added tag "${norm}" for channel ${channelId} and coach ${coachName}`);
    return true;
  } catch (error) {
    console.error("[ADMIN] Error adding user coach tag:", error);
    return false;
  }
};

/**
 * Remove a tag from a user-coach pair
 */
export const removeUserCoachTag = async (
  channelId: string | number,
  coachName: string,
  tag: string
): Promise<boolean> => {
  try {
    const norm = Tags.normalize(tag);
    if (!norm) return false;
    await db
      .deleteFrom("userCoachTags")
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("tag", "=", norm)
      .execute();

    console.log(`[ADMIN] Removed tag "${norm}" for channel ${channelId} and coach ${coachName}`);
    return true;
  } catch (error) {
    console.error("[ADMIN] Error removing user coach tag:", error);
    return false;
  }
};

/**
 * Update tags for a user-coach pair (replace all existing tags)
 */
export const updateUserCoachTags = async (
  channelId: string | number,
  coachName: string,
  tags: string[]
): Promise<boolean> => {
  try {
    // Start a transaction to ensure consistency
    await db.transaction().execute(async (trx) => {
      // Remove all existing tags
      await trx
        .deleteFrom("userCoachTags")
        .where("channelId", "=", Number(channelId))
        .where("coachName", "=", coachName)
        .execute();

      // Add new tags if any
      if (tags.length > 0) {
        const tagValues = tags
          .map(Tags.normalize)
          .filter(tag => tag.length > 0)
          .map(tag => ({
            channelId: Number(channelId),
            coachName,
            tag
          }));

        if (tagValues.length > 0) {
          await trx
            .insertInto("userCoachTags")
            .values(tagValues)
            .execute();
        }
      }
    });

    console.log(`[ADMIN] Updated tags for channel ${channelId} and coach ${coachName}:`, tags);
    return true;
  } catch (error) {
    console.error("[ADMIN] Error updating user coach tags:", error);
    return false;
  }
};

/**
 * Rename a tag for a specific user (channel) or across all users for a coach.
 * If channelId is null, applies coach-wide. Does not create tags if missing.
 */
export const renameUserCoachTag = async (
  channelId: number | null,
  coachName: string,
  from: string,
  to: string
): Promise<{ updated: number }> => {
  const oldTag = Tags.normalize(from);
  const newTag = Tags.normalize(to);
  if (!oldTag || !newTag || oldTag === newTag) return { updated: 0 };

  return await db.transaction().execute(async (trx) => {
    // 1) Find all affected channelIds for this coach/tag
    let selectQ = trx
      .selectFrom("userCoachTags")
      .select(["channelId"]) 
      .where("coachName", "=", coachName)
      .where("tag", "=", oldTag);
    if (channelId != null) selectQ = selectQ.where("channelId", "=", Number(channelId));
    const rows = await selectQ.execute();

    let totalUpdated = 0;

    // 2) For each channel, upsert the destination tag then delete the source tag
    for (const row of rows) {
      const chId = row.channelId as number;

      // Insert destination if missing
      await trx
        .insertInto("userCoachTags")
        .values({ channelId: chId, coachName, tag: newTag })
        .onConflict((oc) => oc.doNothing())
        .execute();

      // Delete the old tag
      await trx
        .deleteFrom("userCoachTags")
        .where("channelId", "=", chId)
        .where("coachName", "=", coachName)
        .where("tag", "=", oldTag)
        .execute();

      totalUpdated += 1;
    }

    return { updated: totalUpdated };
  });
};

/**
 * Get all tags for multiple users for a specific coach (for UserList display)
 */
export const getUsersTagsForCoach = async (
  channelIds: number[],
  coachName: string
): Promise<Record<number, string[]>> => {
  try {
    if (channelIds.length === 0) return {};

    const tags = await db
      .selectFrom("userCoachTags")
      .select(["channelId", "tag"])
      .where("channelId", "in", channelIds)
      .where("coachName", "=", coachName)
      .orderBy("tag", "asc")
      .execute();

    // Group tags by channelId
    const result: Record<number, string[]> = {};
    for (const tag of tags) {
      if (!result[tag.channelId]) {
        result[tag.channelId] = [];
      }
      result[tag.channelId].push(tag.tag);
    }

    return result;
  } catch (error) {
    console.error("[ADMIN] Error fetching users tags for coach:", error);
    return {};
  }
};

/**
 * Get all unique tags across all users for admin analysis
 */
export const getAllUserTags = async (): Promise<string[]> => {
  try {
    const tags = await db
      .selectFrom("userCoachTags")
      .select("tag")
      .distinct()
      .orderBy("tag", "asc")
      .execute();

    return tags.map(t => t.tag);
  } catch (error) {
    console.error("[ADMIN] Error fetching all user tags:", error);
    return [];
  }
};

/**
 * Get all unique tags for a specific coach
 */
export const getCoachUserTags = async (coachName: string): Promise<string[]> => {
  try {
    const tags = await db
      .selectFrom("userCoachTags")
      .select("tag")
      .distinct()
      .where("coachName", "=", coachName)
      .orderBy("tag", "asc")
      .execute();

    return tags.map(t => t.tag);
  } catch (error) {
    console.error("[ADMIN] Error fetching coach user tags:", error);
    return [];
  }
};

/**
 * Get channel IDs for users with specific tags
 */
export const getChannelIdsByTags = async (
  tags: string[],
  coachName?: string
): Promise<number[]> => {
  try {
    if (tags.length === 0) return [];

    let query = db
      .selectFrom("userCoachTags")
      .select("channelId")
      .distinct()
      .where("tag", "in", tags);

    if (coachName) {
      query = query.where("coachName", "=", coachName);
    }

    const result = await query.execute();
    return result.map(r => r.channelId);
  } catch (error) {
    console.error("[ADMIN] Error fetching channel IDs by tags:", error);
    return [];
  }
};

/**
 * Get conversations filtered by tags
 * @param tags - Array of tags to filter by
 * @param fetchMode - Method to use for fetching conversations
 * @param messagesLimit - Number of messages to fetch
 * @param coachName - Optional coach name to filter by
 */
export const getConversationsByTags = async (
  tags: string[],
  fetchMode: 'byChannel' | 'recentOverall' = 'byChannel',
  messagesLimit: number = fetchMode === 'byChannel' ? 40 : 200,
  coachName?: string
) => {
  try {
    console.log(`[ADMIN] Fetching conversations for tags: ${tags.join(', ')}`);
    
    // Get channel IDs for users with the specified tags
    const channelIds = await getChannelIdsByTags(tags, coachName);
    
    if (channelIds.length === 0) {
      console.log(`[ADMIN] No channels found for tags: ${tags.join(', ')}`);
      return {
        conversations: {},
        stats: {
          channelCount: 0,
          totalMessages: 0,
          fetchMode,
          filteredByTags: tags
        }
      };
    }

    console.log(`[ADMIN] Found ${channelIds.length} channels with tags: ${tags.join(', ')}`);

    let groupedConversations: Record<string, any[]> = {};
    let totalMessages = 0;

    if (fetchMode === 'byChannel') {
      // Fetch recent messages for each channel with tags
      groupedConversations = await getRecentMessagesForChannels(channelIds, messagesLimit);
      totalMessages = Object.values(groupedConversations).reduce((sum, messages) => sum + messages.length, 0);
    } else {
      // Fetch recent messages overall, but only from channels with tags
      const messages = await db
        .selectFrom("conversation")
        .selectAll()
        .where("messageType", "=", "message")
        .where("channelId", "in", channelIds)
        .orderBy("date", "desc")
        .limit(messagesLimit)
        .execute();

      totalMessages = messages.length;
      console.log(`[ADMIN] Retrieved ${totalMessages} total messages from tagged channels`);

      // Group messages by channel
      for (let i = messages.length - 1; i >= 0; i--) {
        const message = messages[i];
        const channelId = message.channelId.toString();
        
        if (!groupedConversations[channelId]) {
          groupedConversations[channelId] = [];
        }
        groupedConversations[channelId].push(message);
      }
    }

    // Count channels and total messages
    const channelCount = Object.keys(groupedConversations).length;
    
    console.log(`[ADMIN] Successfully processed ${totalMessages} messages from ${channelCount} tagged channels`);
    
    return { 
      conversations: groupedConversations,
      stats: {
        channelCount,
        totalMessages,
        fetchMode,
        filteredByTags: tags
      }
    };
  } catch (error) {
    console.error("[ADMIN] Error fetching conversations by tags:", error);
    throw error;
  }
};

/**
 * Analyze conversations with tag filtering
 * @param tags - Array of tags to filter by
 * @param query - Analysis query
 * @param options - Analysis options
 */
export const analyzeConversationsByTags = async (
  tags: string[],
  query: string,
  options: AnalysisOptions
) => {
  try {
    console.log(`[ADMIN] Analyzing conversations for tags: ${tags.join(', ')}`);
    
    // Get conversations filtered by tags
    const { conversations } = await getConversationsByTags(
      tags,
      'byChannel', // Use byChannel for analysis to get comprehensive data
      50 // Reasonable limit for analysis
    );

    if (Object.keys(conversations).length === 0) {
      return {
        result: `No conversations found for the specified tags: ${tags.join(', ')}`,
        tokenInfo: {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          responseTime: '0ms'
        }
      };
    }

    // Use the existing analyzeConversations function with filtered data
    return await analyzeConversations(conversations, query, options);
  } catch (error) {
    console.error("[ADMIN] Error analyzing conversations by tags:", error);
    throw error;
  }
};