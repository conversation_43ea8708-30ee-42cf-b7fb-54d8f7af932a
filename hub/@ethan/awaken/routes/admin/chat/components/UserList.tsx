"use client";

import { useState } from "npm:react@canary";
import { ScrollArea, Text } from "@reframe/ui/main.tsx";
import { TagManagementOverlay } from "./TagManagementOverlay.tsx";
import { Tags } from "../../../../lib/utils/tag-utils.ts";

interface User {
  channelId: number;
  userName: string;
  userEmail: string;
  userImage?: string | null;
  userCurrentPlan?: string;
  tags?: string[];
  hasUnseen: {
    regularMessage: boolean;
    userThreadMessage: boolean;
  };

  lastMessage: {
    content: string;
    date: string;
    sender: string;
    type: string;
  };
}

interface UserListProps {
  users: User[];
  onUserSelect: (user: User) => void;
  coach?: {
    name: string;
    email: string;
  };
  onTagsUpdated?: (channelId: number, newTags: string[]) => void;
  onCoachWideRename?: (from: string, to: string) => void;
}

export const UserList: React.FC<UserListProps> = ({ users, onUserSelect, coach, onTagsUpdated, onCoachWideRename }) => {
  // Tag management state
  const [showTagManagement, setShowTagManagement] = useState(false);
  const [selectedUserForTags, setSelectedUserForTags] = useState<User | null>(null);

  // Handle tag management button click
  const handleTagManagement = (user: User) => {
    setSelectedUserForTags(user);
    setShowTagManagement(true);
  };

  // Handle closing tag management
  const handleCloseTagManagement = () => {
    setShowTagManagement(false);
    setSelectedUserForTags(null);
  };

  // Handle tags updated
  const handleTagsUpdated = (channelId: number, newTags: string[]) => {
    onTagsUpdated?.(channelId, newTags);
  };
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // Less than a week
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const truncateMessage = (message: string, maxLength: number = 40) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  if (users.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400">
        <div className="text-center p-8 max-w-sm mx-auto">
          <div className="w-16 h-16 bg-gradient-to-br from-gray-400/20 to-gray-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Text className="text-2xl">💬</Text>
          </div>
          <div className="space-y-2">
            <Text className="block text-lg text-white">No conversations yet</Text>
            <Text className="block text-sm text-gray-400 leading-relaxed">Users will appear here when they message you</Text>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-full">
      <div className="divide-y divide-white/5 w-full">
        {users.map((user) => {
          const isBasic = user.userCurrentPlan?.startsWith('basic_plan_') ?? false;
          const isPremium = user.userCurrentPlan?.startsWith('premium_plan_') ?? false;
          const planClass = isBasic ? 'bg-blue-600 text-white' : 'bg-yellow-500 text-black';
          const planText = isBasic ? 'Basic' : 'Premium';

          return (
            <div
              key={user.channelId}
              onClick={() => onUserSelect(user)}
              className="p-4 hover:bg-white/5 cursor-pointer transition-colors duration-200 md:hover:translate-x-1 md:transition-transform active:bg-white/10"
            >
              <div className="flex items-start gap-3 w-full">
                {/* User Avatar and Tag Button */}
                <div className="flex-shrink-0 relative flex flex-col items-center gap-2">
                  <div className="relative">
                    {user.userImage ? (
                      <img
                        src={user.userImage}
                        alt={user.userName}
                        className="w-12 h-12 rounded-full object-cover border-2 border-white/10 shadow-lg"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
                        <Text className="text-white font-semibold text-sm">
                          {getInitials(user.userName)}
                        </Text>
                      </div>
                    )}
                    {(user.hasUnseen.regularMessage || user.hasUnseen.userThreadMessage) && (
                      user.hasUnseen.userThreadMessage ? (
                        <span className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-orange-500 border-2 border-black flex items-center justify-center">
                          <Text className="text-[9px] font-bold leading-none text-black">DM</Text>
                        </span>
                      ) : (
                        <span className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-orange-500 border-2 border-black"></span>
                      )
                    )}
                  </div>

                  {/* Tag Button */}
                  {coach && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTagManagement(user);
                      }}
                      className="relative p-2 focus:outline-none"
                      title="Manage user tags"
                    >
                      {/* Glassy background effect */}
                      <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-full border border-orange-500/30 shadow-[0_0_8px_rgba(252,163,17,0.3)]"></div>

                      <svg
                        className="relative z-10 w-4 h-4 text-orange-400 transform rotate-12"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                        />
                      </svg>
                    </button>
                  )}
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0 overflow-hidden bg-opacity-100 rounded-lg z-10">
                  {/* Name and Time Row */}
                  <div className="flex items-start justify-between mb-2 gap-2">
                    <Text className="font-semibold text-white flex-1 min-w-0 leading-tight">
                      {user.userName}
                      {user.userCurrentPlan && (isBasic || isPremium) && (
                        <span
                          className={`ml-2 align-middle px-1.5 py-[1.5px] rounded-full text-[10px] font-semibold shadow border border-white/10 whitespace-nowrap ${planClass} bg-black bg-opacity-100 z-20`}
                          style={{ pointerEvents: 'none', zIndex: 20 }}
                        >
                          {planText}
                        </span>
                      )}
                    </Text>
                    <Text className="text-xs text-orange-300 font-medium whitespace-nowrap flex-shrink-0 mt-0.5 bg-black bg-opacity-100 z-10">
                      {formatDate(user.lastMessage.date)}
                    </Text>
                  </div>

                  {/* Last Message Preview */}
                  <div className="flex items-start gap-2 mb-2">
                    {user.lastMessage.sender === 'user' ? (
                      <div className="w-4 h-4 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-blue-400 text-xs">👤</span>
                      </div>
                    ) : (
                      <div className="w-4 h-4 bg-orange-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-orange-400 text-xs">🤖</span>
                      </div>
                    )}
                    <Text className="text-sm text-gray-300 leading-relaxed flex-1 min-w-0 bg-black bg-opacity-100 z-10">
                      {truncateMessage(user.lastMessage.content)}
                    </Text>
                  </div>

                  {/* User Email (if available) */}
                  {user.userEmail && (
                    <Text className="text-xs text-gray-500 break-words bg-black bg-opacity-100 z-10">
                      {user.userEmail}
                    </Text>
                  )}

                  {/* Tags */}
                  {user.tags && user.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {user.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 text-xs bg-orange-500/20 text-orange-300 rounded-full border border-orange-500/30 whitespace-nowrap"
                        >
                          {Tags.display(tag)}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </ScrollArea>

    {/* Tag Management Overlay */}
    {coach && selectedUserForTags && (
      <TagManagementOverlay
        isOpen={showTagManagement}
        onClose={handleCloseTagManagement}
        user={selectedUserForTags}
        coach={coach}
        onTagsUpdated={handleTagsUpdated}
        onCoachWideRename={onCoachWideRename}
      />
    )}
    </>
  );
};