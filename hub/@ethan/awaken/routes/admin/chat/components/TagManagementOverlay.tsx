"use client";

import { useState, useEffect } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { getUserCoachTags, updateUserCoachTags, renameUserCoachTag, getCoachUserTags } from "../../actions/admin-actions.ts";
import { createPortal } from "npm:react-dom@canary";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";
import { Tags } from "../../../../lib/utils/tag-utils.ts";

interface TagManagementOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    channelId: number;
    userName: string;
    userEmail: string;
  };
  coach: {
    name: string;
    email: string;
  };
  onTagsUpdated: (channelId: number, newTags: string[]) => void;
  onCoachWideRename?: (from: string, to: string) => void;
}

export const TagManagementOverlay: React.FC<TagManagementOverlayProps> = ({
  isOpen,
  onClose,
  user,
  coach,
  onTagsUpdated,
  onCoachWideRename,
}) => {
  // Do not attempt to render on the server
  if (typeof document === "undefined") return null;

  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [coachTags, setCoachTags] = useState<string[]>([]);
  const [tagSuggestions, setTagSuggestions] = useState<string[]>([]);
  const [tagInputFocused, setTagInputFocused] = useState<boolean>(false);

  // Load existing tags when overlay opens
  useEffect(() => {
    if (isOpen && user.channelId && coach.name) {
      loadTags();
    }
  }, [isOpen, user.channelId, coach.name]);

  const loadTags = async () => {
    try {
      setLoading(true);
      setError(null);
      const existingTags = await getUserCoachTags(user.channelId, coach.name);
      setTags(existingTags);
      // Also fetch coach-wide tags for autocomplete
      try {
        const allCoachTags = await getCoachUserTags(coach.name);
        // Normalize, unique, sorted
        const unique = Array.from(new Set(allCoachTags.map(Tags.normalize))).sort();
        setCoachTags(unique);
      } catch (e) {
        // Non-fatal
        console.warn("[TagOverlay] Failed to fetch coach tags", e);
      }
    } catch (err) {
      console.error("Error loading tags:", err);
      setError("Failed to load existing tags");
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = () => {
    const norm = Tags.normalize(newTag);
    if (norm && !tags.includes(norm)) {
      setTags(prev => [...prev, norm]);
      setNewTag("");
      setTagSuggestions([]);
    }
  };

  // Compute suggestions based on current input and coach-wide tags
  useEffect(() => {
    const existing = new Set(tags.map(Tags.normalize));
    const partial = Tags.normalize(newTag);
    const base = coachTags.filter(t => !existing.has(t));
    const filtered = partial ? base.filter(t => t.startsWith(partial)) : base;
    // Limit to a reasonable number
    setTagSuggestions(filtered.slice(0, 20));
  }, [newTag, coachTags, tags]);

  const selectSuggestion = (t: string) => {
    const norm = Tags.normalize(t);
    if (!norm) return;
    if (!tags.includes(norm)) {
      setTags(prev => [...prev, norm]);
    }
    setNewTag("");
    setTagSuggestions([]);
  };

  const handleRename = async (oldTag: string, newRaw: string) => {
    const norm = Tags.normalize(newRaw);
    if (!norm || norm === oldTag) return;
    const prevTags = tags;
    setTags(prev => prev.map(t => (t === oldTag ? norm : t)));
    try {
      // Coach-wide rename: pass null channel to apply across all users
      await renameUserCoachTag(null, coach.name, oldTag, norm);
      onCoachWideRename?.(oldTag, norm);
    } catch {
      setTags(prevTags);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const success = await updateUserCoachTags(user.channelId, coach.name, tags);

      if (success) {
        // Small delay to ensure database update has completed
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Notify parent component about the update FIRST
        onTagsUpdated(user.channelId, tags);

        // Wait a bit longer to ensure all state updates propagate properly
        setTimeout(() => {
          onClose();
        }, 300);
      } else {
        setError("Failed to update tags");
      }
    } catch (err) {
      console.error("Error saving tags:", err);
      setError("Failed to save tags");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setNewTag("");
    setError(null);
    onClose();
  };

  // Animation transitions
  const slideTransition = useTransition(isOpen, {
    from: { transform: "translateY(100%)", opacity: 0 },
    enter: { transform: "translateY(0%)", opacity: 1 },
    leave: { transform: "translateY(100%)", opacity: 0 },
    config: {
      tension: 250,
      friction: 35,
      clamp: true,
    },
  });

  const overlay = slideTransition((style, item) =>
    item ? (
      <animated.div
        style={{ ...style, willChange: "transform" }}
        className="fixed inset-x-0 bottom-0 h-[50vh] bg-black/90 backdrop-blur-md border-t border-white/10 rounded-t-3xl flex flex-col z-50"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10 max-w-3xl mx-auto w-full">
          <div className="flex flex-col">
            <span className="text-white font-semibold text-lg mb-1">Manage Tags</span>
            <span className="text-sm text-orange-300">
              {user.userName} • {coach.name}
            </span>
          </div>
          <Button
            variant="ghost"
            css="p-2 hover:bg-white/10 rounded-lg"
            onClick={handleCancel}
          >
            <span className="text-white text-xl">×</span>
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 w-full max-w-3xl mx-auto">
          {/* Add new tag */}
          <div className="space-y-2">
            <Text className="text-sm font-medium text-white">Add New Tag</Text>
            <div className="flex gap-2 relative">
              <input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter tag name..."
                className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
                disabled={saving}
                onFocus={() => setTagInputFocused(true)}
                onBlur={() => setTimeout(() => setTagInputFocused(false), 120)}
              />
              <Button
                variant="ghost"
                onClick={handleAddTag}
                disabled={!newTag.trim() || saving}
                css={`${!newTag.trim() || saving ? "bg-gray-600 text-gray-400 cursor-not-allowed" : "bg-orange-600 hover:bg-orange-700 text-white"} px-4 py-2 rounded-lg transition-colors`}
              >
                Add
              </Button>
              {tagInputFocused && tagSuggestions.length > 0 && (
                <div className="absolute left-0 top-full mt-1 bg-black border border-white/10 rounded shadow max-h-48 overflow-auto z-50 w-full">
                  {tagSuggestions.map((t) => (
                    <div
                      key={t}
                      className="px-3 py-2 text-sm text-white hover:bg-white/10 cursor-pointer"
                      onMouseDown={(e) => { e.preventDefault(); selectSuggestion(t); }}
                    >
                      {Tags.display(t)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Current tags */}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-white">Current Tags</span>
            {loading ? (
              <span className="text-gray-400 text-sm">Loading tags...</span>
            ) : tags.length === 0 ? (
              <span className="text-gray-400 text-sm">No tags assigned</span>
            ) : (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 px-3 py-1 bg-orange-500/20 text-orange-300 rounded-full border border-orange-500/30"
                  >
                    <span className="text-sm">{Tags.display(tag)}</span>
                    <div className="flex items-center gap-1">
                      {/* Inline rename trigger */}
                      <details className="relative">
                        <summary className="list-none">
                          <span
                            role="button"
                            aria-label={`Edit ${tag}`}
                            className="inline-flex items-center justify-center w-9 h-9 rounded-md cursor-pointer text-orange-200 hover:text-white hover:bg-white/10 text-lg"
                          >
                            ✎
                          </span>
                        </summary>
                        <div className="absolute left-0 top-full mt-1 bg-black border border-white/10 rounded p-2 z-50 w-56 shadow-xl whitespace-nowrap">
                          <input
                            defaultValue={tag}
                            className="bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded px-2 py-1 text-xs focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
                            onBlur={(e) => handleRename(tag, e.target.value)}
                            onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); (e.target as HTMLInputElement).blur(); } }}
                          />
                        </div>
                      </details>
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        disabled={saving}
                        aria-label={`Remove ${tag}`}
                        className="inline-flex items-center justify-center w-9 h-9 text-orange-300 hover:text-orange-100 hover:bg-white/10 rounded-md disabled:opacity-50 transition-colors text-lg"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Error message */}
          {error && (
            <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <Text className="text-red-300 text-sm">{error}</Text>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-white/10 p-4 flex gap-3 max-w-3xl mx-auto w-full">
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={saving}
            css={`${saving ? "bg-gray-600 text-gray-400 cursor-not-allowed" : "bg-gray-700 hover:bg-gray-600 text-white"} flex-1 py-2 rounded-lg transition-colors`}
          >
            Cancel
          </Button>
          <Button
            variant="ghost"
            onClick={handleSave}
            disabled={saving}
            css={`${saving ? "bg-gray-600 text-gray-400 cursor-not-allowed" : "bg-orange-600 hover:bg-orange-700 text-white"} flex-1 py-2 rounded-lg transition-colors`}
          >
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </animated.div>
    ) : null
  );

  return createPortal(overlay, document.body);
};
