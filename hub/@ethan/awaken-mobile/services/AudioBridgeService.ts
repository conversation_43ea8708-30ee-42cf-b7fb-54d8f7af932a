import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { AudioRecorderState, AudioRecorderCallbacks } from '../hooks/useExpoAudioRecorder';

export interface AudioRecordingMessage {
  type: 'AUDIO_RECORDING_START' | 'AUDIO_RECORDING_STOP' | 'AUDIO_RECORDING_CANCEL' | 'AUDIO_PLAYBACK_TOGGLE';
  requestId?: string;
}

export interface AudioRecordingResponse {
  type: 'AUDIO_RECORDING_STATE' | 'AUDIO_RECORDING_COMPLETE' | 'AUDIO_RECORDING_ERROR';
  requestId?: string;
  data?: {
    isRecording?: boolean;
    recordingTime?: number;
    audioUri?: string | null;
    audioDuration?: number;
    isPlaying?: boolean;
    playbackProgress?: number;
    isLoading?: boolean;
    audioData?: string | null; // Base64 encoded audio data
    error?: string | null;
  };
}

class AudioBridgeService {
  private static instance: AudioBridgeService;
  private webViewRef: any = null;
  private currentRecorderState: AudioRecorderState | null = null;
  private recorderCallbacks: AudioRecorderCallbacks | null = null;

  public static getInstance(): AudioBridgeService {
    if (!AudioBridgeService.instance) {
      AudioBridgeService.instance = new AudioBridgeService();
    }
    return AudioBridgeService.instance;
  }

  public setWebViewRef(webViewRef: any) {
    this.webViewRef = webViewRef;
    console.log('[AudioBridgeService] WebView reference set');
  }

  public setRecorderState(state: AudioRecorderState, callbacks: AudioRecorderCallbacks) {
    this.currentRecorderState = state;
    this.recorderCallbacks = callbacks;
    console.log('[AudioBridgeService] Recorder state and callbacks updated');
  }

  public getCurrentCallbacks() {
    return this.recorderCallbacks;
  }

  public async handleMessage(message: AudioRecordingMessage): Promise<void> {
    console.log('[AudioBridgeService] Received message:', message);

    try {
      switch (message.type) {
        case 'AUDIO_RECORDING_START':
          await this.startRecording(message.requestId);
          break;
        case 'AUDIO_RECORDING_STOP':
          await this.stopRecording(message.requestId);
          break;
        case 'AUDIO_RECORDING_CANCEL':
          await this.cancelRecording(message.requestId);
          break;
        case 'AUDIO_PLAYBACK_TOGGLE':
          await this.togglePlayback(message.requestId);
          break;
        default:
          console.warn('[AudioBridgeService] Unknown message type:', message.type);
      }
    } catch (error) {
      this.sendErrorToWebView(`Failed to handle message: ${error}`, message.requestId);
    }
  }

  private async startRecording(requestId?: string): Promise<void> {
    console.log('[AudioBridgeService] startRecording called with requestId:', requestId);
    try {
      if (this.recorderCallbacks?.startRecording) {
        console.log('[AudioBridgeService] Calling native startRecording');
        await this.recorderCallbacks.startRecording();
        console.log('[AudioBridgeService] Native startRecording completed');
        this.sendStateToWebView(requestId);
      } else {
        console.error('[AudioBridgeService] Recorder not initialized');
        throw new Error('Recorder not initialized');
      }
    } catch (error) {
      console.error('[AudioBridgeService] startRecording error:', error);
      this.sendErrorToWebView(`Failed to start recording: ${error}`, requestId);
    }
  }

  private async stopRecording(requestId?: string): Promise<void> {
    console.log('[AudioBridgeService] stopRecording called with requestId:', requestId);
    try {
      if (this.recorderCallbacks?.stopRecording) {
        console.log('[AudioBridgeService] Calling native stopRecording');
        await this.recorderCallbacks.stopRecording();
        console.log('[AudioBridgeService] Native stopRecording completed');

        // Wait a moment for state to update, then send complete message with audio data
        setTimeout(async () => {
          await this.sendCompleteToWebView(requestId);
        }, 100);
      } else {
        console.error('[AudioBridgeService] Recorder not initialized');
        throw new Error('Recorder not initialized');
      }
    } catch (error) {
      console.error('[AudioBridgeService] stopRecording error:', error);
      this.sendErrorToWebView(`Failed to stop recording: ${error}`, requestId);
    }
  }

  private async cancelRecording(requestId?: string): Promise<void> {
    console.log('[AudioBridgeService] cancelRecording called with requestId:', requestId);
    try {
      if (this.recorderCallbacks?.cancelRecording) {
        console.log('[AudioBridgeService] Calling native cancelRecording');
        await this.recorderCallbacks.cancelRecording();
        console.log('[AudioBridgeService] Native cancelRecording completed');
        this.sendStateToWebView(requestId);
      } else {
        console.error('[AudioBridgeService] Recorder not initialized');
        throw new Error('Recorder not initialized');
      }
    } catch (error) {
      console.error('[AudioBridgeService] cancelRecording error:', error);
      this.sendErrorToWebView(`Failed to cancel recording: ${error}`, requestId);
    }
  }

  private async togglePlayback(requestId?: string): Promise<void> {
    console.log('[AudioBridgeService] togglePlayback called with requestId:', requestId);
    try {
      if (this.recorderCallbacks?.togglePlayback) {
        console.log('[AudioBridgeService] Calling native togglePlayback');
        await this.recorderCallbacks.togglePlayback();
        console.log('[AudioBridgeService] Native togglePlayback completed');
        this.sendStateToWebView(requestId);
      } else {
        console.error('[AudioBridgeService] Recorder not initialized');
        throw new Error('Recorder not initialized');
      }
    } catch (error) {
      console.error('[AudioBridgeService] togglePlayback error:', error);
      this.sendErrorToWebView(`Failed to toggle playback: ${error}`, requestId);
    }
  }

  private sendStateToWebView(requestId?: string): void {
    if (!this.webViewRef || !this.currentRecorderState) return;

    const response: AudioRecordingResponse = {
      type: 'AUDIO_RECORDING_STATE',
      requestId,
      data: {
        isRecording: this.currentRecorderState.isRecording,
        recordingTime: this.currentRecorderState.recordingTime,
        audioUri: this.currentRecorderState.audioUri,
        audioDuration: this.currentRecorderState.audioDuration,
        isPlaying: this.currentRecorderState.isPlaying,
        playbackProgress: this.currentRecorderState.playbackProgress,
        isLoading: this.currentRecorderState.isLoading,
        error: this.currentRecorderState.error,
      },
    };

    this.postMessageToWebView(response);
  }

  private async sendCompleteToWebView(requestId?: string): Promise<void> {
    if (!this.webViewRef || !this.currentRecorderState) return;

    try {
      let audioData: string | null = null;

      // Get audio data if recording is complete and we have a URI
      if (this.currentRecorderState.audioUri && this.recorderCallbacks?.getAudioData) {
        console.log('[AudioBridgeService] Getting audio data...');
        audioData = await this.recorderCallbacks.getAudioData();
        console.log('[AudioBridgeService] Audio data retrieved, length:', audioData?.length || 0);
      }

      const response: AudioRecordingResponse = {
        type: 'AUDIO_RECORDING_COMPLETE',
        requestId,
        data: {
          isRecording: this.currentRecorderState.isRecording,
          recordingTime: this.currentRecorderState.recordingTime,
          audioUri: this.currentRecorderState.audioUri,
          audioDuration: this.currentRecorderState.audioDuration,
          isPlaying: this.currentRecorderState.isPlaying,
          playbackProgress: this.currentRecorderState.playbackProgress,
          isLoading: this.currentRecorderState.isLoading,
          audioData,
          error: this.currentRecorderState.error,
        },
      };

      this.postMessageToWebView(response);
    } catch (error) {
      console.error('[AudioBridgeService] Failed to send complete message:', error);
      this.sendErrorToWebView(`Failed to get audio data: ${error}`, requestId);
    }
  }

  private sendErrorToWebView(error: string, requestId?: string): void {
    if (!this.webViewRef) return;

    const response: AudioRecordingResponse = {
      type: 'AUDIO_RECORDING_ERROR',
      requestId,
      data: {
        error,
      },
    };

    this.postMessageToWebView(response);
  }

  private postMessageToWebView(response: AudioRecordingResponse): void {
    if (this.webViewRef?.current) {
      const message = JSON.stringify(response);
      this.webViewRef.current.postMessage(message);
      console.log('[AudioBridgeService] Sent message to WebView:', response.type);
    }
  }

  public cleanup(): void {
    this.currentRecorderState = null;
    this.recorderCallbacks = null;
    this.webViewRef = null;
    console.log('[AudioBridgeService] Cleaned up');
  }
}

export default AudioBridgeService.getInstance();